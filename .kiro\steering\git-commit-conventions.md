---
inclusion: manual
---

# Git Commit and Push Conventions

## Critical Rules

- Always run `git add .` from the workspace root to stage changes
- Review staged changes before committing to ensure no unintended files are included
- Format commit titles as `type: brief description` where type is one of:
  - feat: new feature
  - fix: bug fix
  - docs: documentation changes
  - style: formatting, missing semi colons, etc
  - refactor: code restructuring
  - test: adding tests
  - chore: maintenance tasks
- Keep commit title brief and descriptive (max 72 chars)
- Add two line breaks after commit title
- Include a detailed body paragraph explaining:
  - What changes were made
  - Why the changes were necessary
  - Any important implementation details
- End commit message with " -Agent Generated Commit Message"
- Push changes to the current remote branch

## Good Example

```
feat: add user authentication system

Implemented JWT-based user authentication system with secure password hashing
and token refresh functionality. This change provides secure user sessions
and prevents unauthorized access to protected routes. The implementation
uses bcrypt for password hashing and includes proper token expiration handling.

-Agent Generated Commit Message
```

## Avoid

```
updated stuff

fixed some bugs and added features
```
