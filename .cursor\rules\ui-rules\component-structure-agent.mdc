---
description: 
globs: 
alwaysApply: true
---
# 组件结构规范

## 关键规则

- 组件优先使用文件夹结构组织，推荐的组织方式为：`index.vue`、`index.ts`(可选)和`types.ts`(可选)
- 使用TypeScript时，`types.ts`必须包含所有组件相关的类型定义和接口，使用`export type`和`export interface`导出
- 当组件需要全局注册时，`index.ts`需要封装组件安装逻辑，并使用`export default`导出组件
- 支持以下两种组件编写方式：
  - Vue 3风格：`<script setup lang="ts">` (优先，用于submodule/ent-module)
  - Vue 2.7风格：常规`<script>`标签，搭配选项式API或`setup`函数
- 组件样式应使用`<style lang="scss" scoped>`并避免全局污染
- 组件必须具有明确的类型定义，包括props、emits和其他相关类型

## 示例

<example>
组件目录结构示例：
```
src/components/Example/
  ├── index.vue     # 组件主文件
  ├── index.ts      # 导出文件（可选）
  └── types.ts      # 类型定义（可选）
```

types.ts示例：
```typescript
export type ExampleOption = {
  label: string;
  value: string | number;
};

export interface ExampleProps {
  // 选项列表
  options?: ExampleOption[];
  // 默认值
  defaultValue?: string;
}
```

index.ts示例：
```typescript
import Example from "./index.vue";

// Vue 2.x方式
export default Example;

// 带安装方法（Vue插件形式）
Example.install = function(Vue) {
  Vue.component(Example.name, Example);
};
```

Vue 3风格组件示例（适用于ent-module子应用）：
```vue
<template>
  <div class="example">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { ExampleProps } from "./types";

// Vue 2.7没有defineOptions，但script setup语法支持
const props = defineProps<ExampleProps>();
const emit = defineEmits<{
  (e: 'change', value: string): void,
  (e: 'update', value: string): void
}>();

// 组件逻辑
const value = ref(props.defaultValue || '');

// 方法定义
function handleChange(newValue: string) {
  value.value = newValue;
  emit('change', newValue);
}
</script>

<style lang="scss" scoped>
.example {
  // 组件样式
}
</style>
```

Vue 2.7风格组件示例：
```vue
<template>
  <div class="example">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: "Example",
  props: {
    options: {
      type: Array,
      default: () => []
    },
    defaultValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      value: this.defaultValue
    };
  },
  methods: {
    handleChange(newValue) {
      this.value = newValue;
      this.$emit('change', newValue);
    }
  }
}
</script>

<style lang="scss" scoped>
.example {
  // 组件样式
}
</style>
```

Vue 2.7 + Composition API风格：
```vue
<template>
  <div class="example">
    <!-- 组件内容 -->
  </div>
</template>

<script>
import { ref, toRefs } from 'vue';

export default {
  name: "Example",
  props: {
    options: {
      type: Array,
      default: () => []
    },
    defaultValue: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }) {
    const { defaultValue } = toRefs(props);
    const value = ref(defaultValue.value);
    
    function handleChange(newValue) {
      value.value = newValue;
      emit('change', newValue);
    }
    
    return {
      value,
      handleChange
    }
  }
}
</script>

<style lang="scss" scoped>
.example {
  // 组件样式
}
</style>
```
</example>

<example type="invalid">
反面示例 - 不符合规范的组件结构：
```
src/components/Button.vue  # 复杂组件没有使用文件夹结构
```

或使用不一致的命名约定：
```
src/components/re-example/
  └── Component.vue  # 文件名应为index.vue
```

或缺少适当的类型定义（使用TypeScript时）：
```vue
<script setup lang="ts">
// 错误：未定义Props类型
const props = defineProps({
  value: String
});
</script>
```

或选项式API中使用不规范的方式：
```vue
<script>
export default {
  // 错误：不使用setup函数而是使用混合了选项式API和组合式API的方式
  data() { return { foo: 'bar' } },
  setup() {
    const value = ref(''); // 混用了两种方式，导致逻辑分散
    return { value }
  }
}
</script>
```
</example>
