# Kiro Steering Rules

This directory contains steering rules converted from the original Cursor rules in `.cursor/rules/`. These rules guide <PERSON><PERSON>'s behavior and ensure consistent
development practices.

## File Organization

### Always Active Rules

- `global-emoji-communication.md` - Emoji usage guidelines for communication
- `pnpm-package-management.md` - Package management using pnpm instead of npm

### File-Matched Rules (Auto-applied based on file types)

- `typescript-best-practices.md` - TypeScript development best practices
- `typescript-vue27-integration.md` - TypeScript integration with Vue 2.7
- `component-structure.md` - Vue component structure and organization
- `component-organization.md` - Component directory organization rules
- `composables-organization.md` - Composables/hooks organization patterns
- `business-logic-reuse.md` - Business logic reuse principles
- `vue27-composition-api.md` - Vue 2.7 Composition API usage
- `vue27-class-component.md` - Vue 2.7 Class Component patterns
- `pinia-vue27-integration.md` - Pinia state management with Vue 2.7

### Manual Rules (Activated when needed)

- `git-commit-conventions.md` - Git commit message formatting
- `bocha-search-priority.md` - External search using Bocha search engine
- `agile-workflow-pm.md` - Project Manager workflow and PRD management
- `agile-workflow-architect.md` - Architect workflow and architecture documentation
- `rule-generation-standards.md` - Standards for creating new Kiro steering rules

## Rule Inclusion Types

1. **Always** - Applied to all interactions
2. **FileMatch** - Applied when specific file patterns are in context
3. **Manual** - Applied when explicitly referenced or needed

## Original Source

These rules were converted from the Cursor rules located in:

- `.cursor/rules/global-rules/`
- `.cursor/rules/ts-rules/`
- `.cursor/rules/ui-rules/`
- `.cursor/rules/tool-rules/`
- `.cursor/rules/workflows/`
- `.cursor/rules/core-rules/`

## Usage

Kiro automatically applies these steering rules based on their inclusion settings. No manual activation is required for `always` and `fileMatch` rules. Manual
rules can be referenced using `#` context in chat.
