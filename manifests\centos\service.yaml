apiVersion: v1
kind: Service
metadata:
  name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
  # 修改为规划 Namespaces，仅限修改此处
  namespace: __CUST_APP_NAMESPACE__
  labels:
    app: __CUST_APP_NAME__
    ref: __CI_ENVIRONMENT_SLUG__
  annotations:
    # 此处为后续监控视图配置，端口随不同服务端口变动
    prometheus.io/scrape: "true"
    prometheus.io/port: "9000"
    prometheus.io/scheme: "http"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
    - name: __CI_PROJECT_NAME__
      port: 80
      protocol: TCP
      targetPort: http-metrics
  selector:
    app: __CUST_APP_NAME__
    ref: __CI_ENVIRONMENT_SLUG__
