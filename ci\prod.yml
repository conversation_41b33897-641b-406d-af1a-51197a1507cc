# release
build_release_job:
  image: registry.qixin007.com/enterprise/base/node:runner-14.21.3
  stage: build_release
  variables:
  script:
    - export NODE_OPTIONS="--max_old_space_size=8192"
    - export CUST_IMAGE_TAG=${CI_COMMIT_REF_NAME//*-/rc-release-}
    - export CUST_APP_IMAGE_NAME=$CI_K8S_REGISTRY/$CI_PROJECT_PATH:$CUST_IMAGE_TAG
    - export CUST_APP_STATIC_NAME=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME:$CUST_IMAGE_TAG
    - CUST_TAG_STATUS=`curl -X GET -H "accept:application/json" -k
      "https://$CI_K8S_REGISTRY/api/v2.0/projects/$CI_PROJECT_NAMESPACE/repositories/$CI_PROJECT_NAME/artifacts/$CUST_IMAGE_TAG" -o /dev/null -s -w
      %{http_code}`
    - export
    - chmod a+x ./tools/build.sh
    - source ./tools/build.sh $CUST_TAG_STATUS $CI_PROJECT_PATH $CUST_IMAGE_TAG $CUST_APP_IMAGE_NAME $CUST_APP_STATIC_NAME $CUST_CDN_DIR $CUST_BUILD_DIR
      $CUST_DEPLOY_PASSWORD $CUST_REGISTRY_ADDRESS $CUST_REGISTRY_DIR
  only:
    - /^rc-release-.*$/
    - /^rc-prod-.*$/
    - /^rc-plugin-.*$/
    - /^rc-plugincdn-.*$/
  except:
    - branches
  tags:
    - webb-docker

deploy_prod_job:
  image: registry.qixin007.com/enterprise/base/node:runner-14.21.3
  stage: deploy_prod
  environment:
    name: prod
    url: https://b.qixin.com/
  variables:
    CUST_ENV: prod
    CUST_SERVICE: saas
    APP_URL: b.qixin.com
    CUST_APP_NAME: $CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
    CUST_APP_IMAGE_NAME: $CI_K8S_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_NAME
    CUST_APP_SERVER_NAME: $CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
  script:
    - export
    - export CUST_IMAGE_TAG=${CI_COMMIT_REF_NAME//*-/rc-release-}
    - export CUST_APP_IMAGE_NAME=$CI_K8S_REGISTRY/$CI_PROJECT_PATH:$CUST_IMAGE_TAG
    - export CUST_APP_STATIC_NAME=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME:$CUST_IMAGE_TAG
    - export
    # 把仓库里的文件同步到本地
    - export RSYNC_PASSWORD=$CUST_DEPLOY_PASSWORD;/usr/bin/rsync -aczqP --stats --port=5757
      $CUST_DEPLOY_PASSWORD@$CUST_REGISTRY_ADDRESS::$CUST_REGISTRY_DIR/$CUST_APP_STATIC_NAME ./
    # 把本地的文件同步到目标服务器
    - export RSYNC_PASSWORD=$CUST_DEPLOY_PASSWORD;/usr/bin/rsync -aczqP --stats --port=5757 ./$CUST_APP_STATIC_NAME/
      $CUST_DEPLOY_PASSWORD@$CUST_CDN_ADDRESS_PROD::$CUST_CDN_DIR
    - cd manifests/
    - kubectl version
    - kubectl config view
    - kubectl config get-contexts
    - sed -i "s|__CUST_ENV__|${CUST_ENV}|" deployment.yaml
    - sed -i "s|__CUST_SERVICE__|${CUST_SERVICE}|" deployment.yaml
    - sed -i "s|__CI_PROJECT_NAME__|${CI_PROJECT_NAME}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_APP_NAME__|${CUST_APP_NAME}|" deployment.yaml service.yaml auto-scaler.yaml
    - sed -i "s|__CI_ENVIRONMENT_SLUG__|${CI_ENVIRONMENT_SLUG}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_APP_NAMESPACE__|${CUST_APP_NAMESPACE}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_REPLICAS__|${CUST_PROD_MIN_REPLICAS}|" deployment.yaml
    - sed -i "s|__CUST_PORT_APP__|${CUST_PORT_APP}|" deployment.yaml service.yaml
    - sed -i "s|__CUST_APP_IMAGE_NAME__|${CUST_APP_IMAGE_NAME}|" deployment.yaml
    - sed -i "s|__APP_URL__|${APP_URL}|" ingress.yaml
    - sed -i "s|__CI_PROJECT_NAMESPACE__|${CI_PROJECT_NAMESPACE}|" auto-scaler.yaml
    - sed -i "s|__CUST_MIN_RC__|${CUST_PROD_MIN_REPLICAS}|" auto-scaler.yaml
    - sed -i "s|__CUST_MAX_RC__|${CUST_PROD_MAX_REPLICAS}|" auto-scaler.yaml
    - sed -i "s|__CI_PROJECT_NAME__|${CI_PROJECT_NAME}|" auto-scaler.yaml
    - sed -i "s|__AUTO_SCALER_AVERAGE_UTILIZATION__|${AUTO_SCALER_AVERAGE_UTILIZATION}|" auto-scaler.yaml
    # 部署至kubernetes-qxb-saasweb
    - kubectl config use-context kubernetes-qxb-saasweb
    - kubectl apply -f deployment.yaml
    - kubectl apply -f service.yaml
    - kubectl apply -f ingress.yaml
    - kubectl apply -f auto-scaler.yaml
    - kubectl rollout status -f deployment.yaml
    - kubectl get all -A -l ref=${CI_ENVIRONMENT_SLUG}
  when: manual
  only:
    - /^rc-release-.*$/
    - /^rc-prod-.*$/
  except:
    - branches
  tags:
    - webb-docker

deploy_plugin_job:
  image: registry.qixin007.com/enterprise/base/node:runner-14.21.3
  stage: deploy_plugin
  environment:
    name: plugin
    url: http://b-plugin.qixin.com/
  variables:
    CUST_ENV: prod
    CUST_SERVICE: plugin
    APP_URL: b-plugin.qixin.com
    CUST_APP_NAME: $CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
    CUST_APP_IMAGE_NAME: $CI_K8S_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_NAME
    CUST_APP_SERVER_NAME: $CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
  script:
    - export
    - export CUST_IMAGE_TAG=${CI_COMMIT_REF_NAME//*-/rc-release-}
    - export CUST_APP_IMAGE_NAME=$CI_K8S_REGISTRY/$CI_PROJECT_PATH:$CUST_IMAGE_TAG
    - export CUST_APP_STATIC_NAME=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME:$CUST_IMAGE_TAG
    - export
    # 把仓库里的文件同步到本地
    - export RSYNC_PASSWORD=$CUST_DEPLOY_PASSWORD;/usr/bin/rsync -aczqP --stats --port=5757
      $CUST_DEPLOY_PASSWORD@$CUST_REGISTRY_ADDRESS::$CUST_REGISTRY_DIR/$CUST_APP_STATIC_NAME ./
    # 把本地的文件同步到目标服务器
    - export RSYNC_PASSWORD=$CUST_DEPLOY_PASSWORD;/usr/bin/rsync -aczqP --stats --port=5757 ./$CUST_APP_STATIC_NAME/
      $CUST_DEPLOY_PASSWORD@$CUST_CDN_ADDRESS_PROD::$CUST_CDN_DIR
    - cd manifests/
    - kubectl version
    - kubectl config view
    - kubectl config get-contexts
    - sed -i "s|__CUST_ENV__|${CUST_ENV}|" deployment.yaml
    - sed -i "s|__CUST_SERVICE__|${CUST_SERVICE}|" deployment.yaml
    - sed -i "s|__CI_PROJECT_NAME__|${CI_PROJECT_NAME}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_APP_NAME__|${CUST_APP_NAME}|" deployment.yaml service.yaml auto-scaler.yaml
    - sed -i "s|__CI_ENVIRONMENT_SLUG__|${CI_ENVIRONMENT_SLUG}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_APP_NAMESPACE__|${CUST_APP_NAMESPACE}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_REPLICAS__|${CUST_PROD_MIN_REPLICAS}|" deployment.yaml
    - sed -i "s|__CUST_PORT_APP__|${CUST_PORT_APP}|" deployment.yaml service.yaml
    - sed -i "s|__CUST_APP_IMAGE_NAME__|${CUST_APP_IMAGE_NAME}|" deployment.yaml
    - sed -i "s|__APP_URL__|${APP_URL}|" ingress.yaml
    - sed -i "s|__CI_PROJECT_NAMESPACE__|${CI_PROJECT_NAMESPACE}|" auto-scaler.yaml
    - sed -i "s|__CUST_MIN_RC__|${CUST_PROD_MIN_REPLICAS}|" auto-scaler.yaml
    - sed -i "s|__CUST_MAX_RC__|${CUST_PROD_MAX_REPLICAS}|" auto-scaler.yaml
    - sed -i "s|__CI_PROJECT_NAME__|${CI_PROJECT_NAME}|" auto-scaler.yaml
    - sed -i "s|__AUTO_SCALER_AVERAGE_UTILIZATION__|${AUTO_SCALER_AVERAGE_UTILIZATION}|" auto-scaler.yaml
    # 部署至kubernetes-qxb-saasweb
    - kubectl config use-context kubernetes-qxb-saasweb
    - kubectl apply -f deployment.yaml
    - kubectl apply -f service.yaml
    - kubectl apply -f ingress.yaml
    - kubectl apply -f auto-scaler.yaml
    - kubectl rollout status -f deployment.yaml
    - kubectl get all -A -l ref=${CI_ENVIRONMENT_SLUG}
  when: manual
  only:
    - /^rc-release-.*$/
    - /^rc-plugin-.*$/
  except:
    - branches
  tags:
    - webb-docker

deploy_plugincdn_job:
  image: registry.qixin007.com/enterprise/base/node:runner-14.21.3
  stage: deploy_plugin_cdn
  environment:
    name: plugincdn
    url: http://per-plugin.qixin.com/
  variables:
    CUST_ENV: prod
    CUST_SERVICE: pluginCdn
    APP_URL: per-plugin.qixin.com
    CUST_APP_NAME: $CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
    CUST_APP_IMAGE_NAME: $CI_K8S_REGISTRY/$CI_PROJECT_PATH:$CI_COMMIT_REF_NAME
    CUST_APP_SERVER_NAME: $CI_PROJECT_NAME-$CI_ENVIRONMENT_NAME
  script:
    - export
    - kubectl version
    - kubectl config view
    - export CUST_IMAGE_TAG=${CI_COMMIT_REF_NAME//*-/rc-release-}
    - export CUST_APP_IMAGE_NAME=$CI_K8S_REGISTRY/$CI_PROJECT_PATH:$CUST_IMAGE_TAG
    - export CUST_APP_STATIC_NAME=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME:$CUST_IMAGE_TAG
    - export
    # 把仓库里的文件同步到本地
    - export RSYNC_PASSWORD=$CUST_DEPLOY_PASSWORD;/usr/bin/rsync -aczqP --stats --port=5757
      $CUST_DEPLOY_PASSWORD@$CUST_REGISTRY_ADDRESS::$CUST_REGISTRY_DIR/$CUST_APP_STATIC_NAME ./
    # 把本地的文件同步到目标服务器
    - export RSYNC_PASSWORD=$CUST_DEPLOY_PASSWORD;/usr/bin/rsync -aczqP --stats --port=5757 ./$CUST_APP_STATIC_NAME/
      $CUST_DEPLOY_PASSWORD@$CUST_CDN_ADDRESS_PROD::$CUST_CDN_DIR
    - cd manifests/
    - kubectl version
    - kubectl config view
    - kubectl config get-contexts
    - sed -i "s|__CUST_ENV__|${CUST_ENV}|" deployment.yaml
    - sed -i "s|__CUST_SERVICE__|${CUST_SERVICE}|" deployment.yaml
    - sed -i "s|__CI_PROJECT_NAME__|${CI_PROJECT_NAME}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_APP_NAME__|${CUST_APP_NAME}|" deployment.yaml service.yaml auto-scaler.yaml
    - sed -i "s|__CI_ENVIRONMENT_SLUG__|${CI_ENVIRONMENT_SLUG}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_APP_NAMESPACE__|${CUST_APP_NAMESPACE}|" deployment.yaml service.yaml ingress.yaml auto-scaler.yaml
    - sed -i "s|__CUST_REPLICAS__|${CUST_PROD_MIN_REPLICAS}|" deployment.yaml
    - sed -i "s|__CUST_PORT_APP__|${CUST_PORT_APP}|" deployment.yaml service.yaml
    - sed -i "s|__CUST_APP_IMAGE_NAME__|${CUST_APP_IMAGE_NAME}|" deployment.yaml
    - sed -i "s|__APP_URL__|${APP_URL}|" ingress.yaml
    - sed -i "s|__CI_PROJECT_NAMESPACE__|${CI_PROJECT_NAMESPACE}|" auto-scaler.yaml
    - sed -i "s|__CUST_MIN_RC__|${CUST_PROD_MIN_REPLICAS}|" auto-scaler.yaml
    - sed -i "s|__CUST_MAX_RC__|${CUST_PROD_MAX_REPLICAS}|" auto-scaler.yaml
    - sed -i "s|__CI_PROJECT_NAME__|${CI_PROJECT_NAME}|" auto-scaler.yaml
    - sed -i "s|__AUTO_SCALER_AVERAGE_UTILIZATION__|${AUTO_SCALER_AVERAGE_UTILIZATION}|" auto-scaler.yaml
    # 部署至kubernetes-qxb-saasweb
    - kubectl config use-context kubernetes-qxb-saasweb
    - kubectl apply -f deployment.yaml
    - kubectl apply -f service.yaml
    - kubectl apply -f ingress.yaml
    - kubectl apply -f auto-scaler.yaml
    - kubectl rollout status -f deployment.yaml
    - kubectl get all -A -l ref=${CI_ENVIRONMENT_SLUG}
  when: manual
  only:
    - /^rc-release-.*$/
    - /^rc-plugincdn-.*$/
  except:
    - branches
  tags:
    - webb-docker
