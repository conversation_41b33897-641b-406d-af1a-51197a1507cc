---
inclusion: fileMatch
fileMatchPattern: '**/*.{ts,js,vue}'
---

# TypeScript 最佳实践

## 关键规则

- 使用严格类型检查，在 tsconfig.json 中设置`"strict": true`
- 对可能被扩展的对象定义优先使用接口（interface）而非类型别名（type）
- 总是为函数和方法定义明确的返回类型
- 对于简单的标志值使用联合类型而不是枚举
- 对复杂状态管理使用可区分联合类型（discriminated unions）
- 永远不要使用`any`类型，对于真正未知的类型使用`unknown`
- 始终明确处理 null 和 undefined 情况
- 使用 readonly 修饰符标记不可变属性和数组
- 使用自定义错误类型实现错误处理
- 使用类型守卫进行运行时类型检查
- 保持泛型简单且约束明确
- 在适当的情况下使用 Pick、Omit、Partial 等工具类型
- 使用 JSDoc 注释文档化复杂类型和所有公共函数、类及接口

## Vue 2.7 与 TypeScript 结合使用规则

- 在 Vue 组件中使用 TypeScript 时，优先使用 Vue 2.7 的 Composition API
- 为组件的 Props 使用类型定义，可以使用 PropType 辅助类型
- 使用扩展的 Window 或全局类型时，确保放在合适的声明文件中（如 shims-vue.d.ts）
- 为事件处理函数明确指定 Event 类型，避免使用 any
- 在 setup 函数中返回的对象显式定义类型（避免推断错误）
- 对于复杂的计算属性，明确指定返回类型
- 为自定义组件添加正确的类型声明

## 良好的实践示例

```typescript
// 使用接口定义明确的类型
interface UserState {
  readonly id: string
  name: string
  email: string | null
  preferences: ReadonlyArray<string>
}

// 使用可区分联合类型与类型守卫
interface RequestPending {
  status: 'pending'
}

interface RequestSuccess {
  status: 'success'
  data: UserState
}

interface RequestError {
  status: 'error'
  error: Error
}

type RequestState = RequestPending | RequestSuccess | RequestError

function isSuccess(state: RequestState): state is RequestSuccess {
  return state.status === 'success'
}

// 有约束的泛型
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
  return obj[key]
}
```

## Vue 2.7 组件类型定义示例

```typescript
import { PropType } from 'vue'

interface TableColumn {
  field: string
  title: string
  width?: number
}

export default {
  name: 'DataTable',
  props: {
    columns: {
      type: Array as PropType<TableColumn[]>,
      required: true
    },
    data: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => []
    }
  },
  setup(props) {
    // 明确类型的计算属性
    const visibleColumns = computed<TableColumn[]>(() => {
      return props.columns.filter(col => !col.hidden)
    })

    return {
      visibleColumns
    }
  }
}
```

## 避免的不良实践

```typescript
// 不良实践 - 使用any类型
function processData(data: any) {
  return data.someProperty
}

// 不良实践 - 未处理null情况
function getUserName(user: { name: string | null }): string {
  return user.name.toUpperCase() // 可能崩溃
}

// 不良实践 - 未经检查的类型断言
function processResponse(response: unknown) {
  const data = response as { id: number }
  return data.id
}

// 不良实践 - 没有类型安全的可变数组
const items = []
items.push(123)
items.push('string') // 混合类型数组
```
