---
description: 
globs: 
alwaysApply: true
---
---
description: 此规则规定了Vue3组合式函数(composables)的组织结构和代码放置位置。应在以下情况应用：(1)创建新的组合式函数，(2)重构现有业务逻辑到组合式函数，(3)移动或重组现有的组合式函数，(4)评估代码结构时。此规则确保代码的可维护性、可重用性和组织清晰度，对于大型应用的代码管理和团队协作至关重要。
globs: 
alwaysApply: false
---

# Composables代码组织规范
## 关键规则

- 页面特定的复杂业务逻辑必须抽离到对应页面目录下的`composables`文件夹中
- 模块内可复用的组合式函数必须放置在该模块根目录下的`composables`文件夹中
- 全局通用的组合式函数必须放置在`src/hooks`或`src/core/hook`目录中
- 微前端子应用的组合式函数必须放置在`src/submodule/ent-module/components/{相关组件}/hooks`目录中
- 所有组合式函数文件名必须以`use`前缀开头，采用驼峰命名法（如`useUserData.ts`）
- 组合式函数必须有明确的返回值类型定义
- 组合式函数应遵循单一职责原则，处理特定的逻辑关注点
- 复杂的组合式函数应进一步拆分为更小的函数，以提高可维护性
- 组合式函数的命名应明确表达其功能和用途
- 支持Vue 2.7和Vue 3的组合式API写法，保持一致的代码风格

## 示例

<example>
✅ 正确的组合式函数组织:

// 页面特定的业务逻辑
src/routes/device/manage/pages/tiantong/composables/useDeviceFilterLogic.ts
src/routes/device/manage/pages/tiantong/composables/useDeviceTableActions.ts

// 模块内公共复用逻辑
src/routes/device/manage/composables/useDevicePermissions.ts
src/routes/traffic/composables/useTrafficStatistics.ts

// 全局通用逻辑
src/hooks/useDialog.ts
src/hooks/usePagination.ts
src/core/hook/useRoute.ts

// 微前端子应用逻辑
src/submodule/ent-module/components/app-ai-chat/hooks/useChat.ts
src/submodule/ent-module/components/app-ai-chat/hooks/useScroll.ts

// 组合式函数示例：
```typescript
// src/routes/device/manage/composables/useDeviceData.ts
import { ref, computed, onMounted } from 'vue'
import type { DeviceInfo } from '../types'
import { fetchDevices } from '@/api/methods/device'

export function useDeviceData(limit: number = 10) {
  const devices = ref<DeviceInfo[]>([])
  const loading = ref(true)
  const error = ref<Error | null>(null)
  
  const activeDevices = computed(() => 
    devices.value.filter(device => device.status === 'active')
  )
  
  const loadDevices = async () => {
    loading.value = true
    try {
      devices.value = await fetchDevices(limit)
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }
  
  onMounted(loadDevices)
  
  return {
    devices,
    activeDevices,
    loading,
    error,
    loadDevices
  }
}
```

// Vue 2.7核心hook示例
```typescript
// src/core/hook/index.ts
import { getCurrentInstance } from 'vue'

// 访问route
export const useRoute = () => {
  const vm = getCurrentInstance()
  if (!vm) throw new Error('must be called in setup')
  return vm.proxy.$route
}
```
</example>

<example type="invalid">
❌ 错误的组合式函数组织:

// 错误：页面特定逻辑不应放在全局hooks中
src/hooks/useDeviceManageTiantongFilters.ts

// 错误：模块公共逻辑不应放在页面特定目录中
src/routes/traffic/daily-traffic/pages/beidou/composables/useTrafficCommonStats.ts

// 错误：未使用composables或hooks目录组织
src/routes/device/manage/useDeviceLogic.ts
src/submodule/ent-module/components/useChartData.ts

// 错误：文件名不以use前缀开始
src/hooks/deviceUtils.ts
src/core/hook/routeHelper.ts

// 错误：未明确类型且组织混乱的实现:
```typescript
// src/routes/device/manageDevices.ts
import { ref } from 'vue'

// 错误：未使用use前缀，未放在正确位置，未定义类型
export function getDeviceData() {
  const devices = ref([])  // 错误：未指定类型
  const loading = ref(true)
  
  // 错误：混合了多个不相关的功能
  function loadData() {
    // 加载设备数据...
  }
  
  function updateUser() {
    // 更新用户信息...
  }
  
  function handlePermissions() {
    // 处理权限...
  }
  
  return {
    devices,
    loading,
    loadData,
    updateUser,
    handlePermissions
  }
}
```
</example>
