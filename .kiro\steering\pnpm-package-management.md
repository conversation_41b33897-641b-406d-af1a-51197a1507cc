---
inclusion: always
---

# PNPM 包管理规范

## 关键规则

- 在终端中执行包管理命令时，必须使用 pnpm 而非 npm
- 安装依赖包时，使用`pnpm add`而非`npm install`
- 安装开发依赖时，使用`pnpm add -D`而非`npm install --save-dev`
- 执行脚本命令时，使用`pnpm run`或直接`pnpm`命令，而非`npm run`
- 更新依赖时，使用`pnpm update`而非`npm update`
- 初始化项目时，使用`pnpm init`而非`npm init`
- 全局安装包时，使用`pnpm add -g`而非`npm install -g`
- 执行批量脚本时，优先使用 pnpm 提供的过滤和并行能力
- 在 CI/CD 环境中，保持使用 pnpm 以确保环境一致性
- 若必须使用 npm 特定命令，应在注释中说明原因
- 如果需要重新运行应用程序，给出提示即可，不用自动重新运行应用程序

## 正确示例

```bash
# 安装项目依赖
pnpm install

# 添加新依赖
pnpm add axios

# 添加开发依赖
pnpm add -D typescript

# 运行开发服务器
pnpm dev

# 运行构建命令
pnpm build

# 执行自定义脚本
pnpm run lint
```

## 避免使用

```bash
# 错误：使用npm安装依赖
npm install

# 错误：使用npm添加依赖
npm install axios

# 错误：使用npm添加开发依赖
npm install --save-dev typescript

# 错误：使用npm运行脚本
npm run dev
```
