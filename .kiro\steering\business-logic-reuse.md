---
inclusion: fileMatch
fileMatchPattern: '**/*.{vue,ts,js}'
---

# 业务逻辑复用原则

## 关键规则

- 永远以业务逻辑复用性为第一原则，对相似功能进行抽象和泛化
- 将多个页面共用的业务逻辑必须抽离到适当的 composables 文件中
- 页面组件应主要由多个 composables 组合而成，保持页面代码简洁清晰
- 业务逻辑应按功能职责拆分成多个较小的 composables，而非单个大型 composable
- 所有 composables 必须定义明确的输入参数和返回值类型
- 抽象出的 composables 应具有足够的灵活性，通过参数控制细节差异
- 修改共用逻辑时，必须评估其对所有使用处的影响，确保一处修改多处正确生效
- 公共业务逻辑应有充分的单元测试覆盖，确保稳定性
- 支持 Vue 2.7 的组合式 API 和 Vue 3 的 script setup 语法，保持一致的代码风格

## 正确的业务逻辑复用示例

**页面级别的组合：使用多个复用的 composables（使用 script setup 语法）**

```vue
<!-- src/routes/device/manage/pages/tiantong/index.vue -->
<script setup lang="ts">
  import { useDeviceList } from '../../../composables/useDeviceList'
  import { useDeviceFilter } from '../../../composables/useDeviceFilter'
  import { useDeviceActions } from '../../../composables/useDeviceActions'
  import { usePermission } from '@/hooks/usePermission'

  // 获取设备列表逻辑
  const { devices, loading, pagination, fetchDevices } = useDeviceList('tiantong')

  // 过滤逻辑
  const { filterForm, filterVisible, applyFilter, resetFilter } = useDeviceFilter(fetchDevices)

  // 设备操作逻辑
  const { handleEdit, handleDelete, handleExport } = useDeviceActions('tiantong')

  // 权限控制
  const { hasPermission } = usePermission('device:tiantong')
</script>
```

**抽象的可复用业务逻辑**

```typescript
// src/routes/device/manage/composables/useDeviceList.ts
import { ref, reactive, onMounted } from 'vue'
import type { Device, PaginationConfig } from '../types'
import { getDeviceList } from '@/api/methods/device'

export function useDeviceList(platformType: string) {
  const devices = ref<Device[]>([])
  const loading = ref(false)
  const pagination = reactive<PaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0
  })

  const fetchDevices = async (params = {}) => {
    loading.value = true
    try {
      const { data, total } = await getDeviceList({
        platformType,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params
      })
      devices.value = data
      pagination.total = total
    } catch (error) {
      console.error('获取设备列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    fetchDevices()
  })

  return {
    devices,
    loading,
    pagination,
    fetchDevices
  }
}
```
