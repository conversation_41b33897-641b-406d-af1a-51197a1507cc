---
description: 
globs: 
alwaysApply: false
---
---
description: 此规则适用于在Vue 2.7项目中使用TypeScript的场景。应在以下情况应用：(1)创建新的TypeScript文件，(2)修改现有TypeScript代码，(3)为JavaScript代码添加类型，(4)定义组件props和emit类型。此规则确保类型安全和代码质量，对于大型项目的可维护性尤为重要。
globs: 
alwaysApply: false
---

# TypeScript 与 Vue 2.7 集成规范

## 关键规则

- 所有Vue组件文件使用`<script lang="ts">`声明TypeScript支持
- 使用`defineComponent`包装组件以获得更好的类型推导
- 使用`PropType<T>`为组件props提供复杂类型定义
- 明确定义所有函数参数和返回类型
- 为响应式数据提供明确的类型注解：`ref<string>('')`, `reactive<User>({})`
- 使用接口（interface）而非类型别名（type）定义对象结构，除非需要联合类型
- 当使用类组件时，应用`vue-property-decorator`和`vue-class-component`
- 使用TypeScript命名空间（namespace）组织相关的接口和类型
- API请求和响应应有明确的类型定义，使用`Awaited<ReturnType<T>>`获取Promise解析类型
- 使用类型断言（`as`）而非类型转换，如`userData as User`
- 事件处理函数参数使用具体类型，而非`any`或`unknown`
- 避免使用`any`，优先使用`unknown`配合类型守卫
- 组件模板引用使用泛型：`ref<HTMLElement | null>(null)`
- 使用类型交叉（`&`）组合类型，而非继承和扩展（extends）

## 示例

<example>
正确的TypeScript与Vue 2.7集成:

// 组件中的TypeScript使用
```vue
<template>
  <!-- 组件模板 -->
</template>

<script lang="ts">
import { defineComponent, ref, PropType, computed } from 'vue'

// 定义接口
interface UserData {
  id: number
  name: string
  email: string
  role: 'admin' | 'user' | 'guest'
}

// 使用defineComponent
export default defineComponent({
  name: 'UserProfile',
  
  props: {
    user: {
      type: Object as PropType<UserData>,
      required: true
    },
    isEditable: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['update:user', 'delete'],
  
  setup(props, { emit }) {
    // 带类型的响应式数据
    const isEditing = ref<boolean>(false)
    const editableUser = ref<UserData>({ ...props.user })
    
    // 带类型的计算属性
    const isAdmin = computed<boolean>(() => props.user.role === 'admin')
    
    // 带类型的函数
    function startEditing(): void {
      isEditing.value = true
      editableUser.value = { ...props.user }
    }
    
    // 带类型的事件处理函数
    function handleSubmit(event: Event): void {
      event.preventDefault()
      emit('update:user', editableUser.value)
      isEditing.value = false
    }
    
    // 带类型的异步函数
    async function fetchUserDetails(): Promise<void> {
      try {
        const response = await fetch(`/api/users/${props.user.id}`)
        const data = await response.json() as UserData
        editableUser.value = { ...data }
      } catch (error) {
        console.error('Failed to fetch user details:', error)
      }
    }
    
    return {
      isEditing,
      editableUser,
      isAdmin,
      startEditing,
      handleSubmit,
      fetchUserDetails
    }
  }
})
</script>
```

// 类型定义文件 (types.ts)
```typescript
// 使用命名空间组织相关类型
export namespace User {
  export interface Profile {
    id: number
    name: string
    email: string
    avatar?: string
    role: Role
  }
  
  export type Role = 'admin' | 'user' | 'guest'
  
  export interface Settings {
    theme: 'light' | 'dark' | 'system'
    notifications: boolean
    language: string
  }
}

// API响应类型
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 使用类型交叉组合类型
export type UserWithSettings = User.Profile & User.Settings
```

// 类组件示例
```vue
<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { User } from '@/types'

@Component
export default class UserComponent extends Vue {
  @Prop({ type: Object, required: true })
  private user!: User.Profile
  
  private isLoading: boolean = false
  
  @Watch('user.id')
  private onUserIdChange(newId: number, oldId: number): void {
    if (newId !== oldId) {
      this.loadUserData()
    }
  }
  
  private async loadUserData(): Promise<void> {
    this.isLoading = true
    try {
      // Implementation...
    } finally {
      this.isLoading = false
    }
  }
  
  mounted(): void {
    this.loadUserData()
  }
}
</script>
```
</example>

<example type="invalid">
错误的TypeScript与Vue 2.7集成:

// 错误: 未使用lang="ts"声明
```vue
<script>
// 没有声明TypeScript支持
import { defineComponent } from 'vue'

export default defineComponent({
  // ...组件定义
})
</script>
```

// 错误: 类型定义不明确
```vue
<script lang="ts">
import { defineComponent, ref } from 'vue'

export default defineComponent({
  setup() {
    const user = ref({}) // 错误: 缺少类型定义
    
    function updateUser(data) { // 错误: 参数缺少类型
      user.value = data
      return true // 错误: 缺少返回类型声明
    }
    
    return { user, updateUser }
  }
})
</script>
```

// 错误: 使用any类型
```typescript
function processData(data: any): any {
  // 使用any类型，丧失了类型安全
  return data.map((item: any) => item.value)
}
```

// 错误: Props类型定义
```vue
<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  props: {
    user: Object, // 错误: 未使用PropType提供具体类型
    config: {
      type: Array // 错误: 未提供数组元素类型
    }
  }
})
</script>
```

// 错误: 不当使用类组件
```vue
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'

@Component
export default class UserList extends Vue {
  users = [] // 错误: 缺少类型注解
  
  mounted = () => { // 错误: 生命周期钩子使用箭头函数
    this.loadUsers()
  }
  
  loadUsers() { // 错误: 缺少返回类型
    // 实现...
  }
}
</script>
```
</example>
