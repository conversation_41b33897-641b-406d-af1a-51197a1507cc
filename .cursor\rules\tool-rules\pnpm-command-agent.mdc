---
description: 此规则适用于在终端中执行包管理器命令的场景。应在以下情况应用：(1)用户需要安装依赖包，(2)用户需要运行脚本命令，(3)用户需要更新或管理项目依赖，(4)在Cursor终端中执行任何与包管理相关的操作。此规则确保项目包管理一致性，避免使用npm导致的锁文件冲突和版本不一致问题。对于项目依赖管理、脚本执行和开发环境一致性尤为重要。
globs: 
alwaysApply: false
---

# 在Cursor终端中使用PNPM命令规范

## 关键规则

- 在Cursor终端中执行包管理命令时，必须使用pnpm而非npm
- 安装依赖包时，使用`pnpm add`而非`npm install`
- 安装开发依赖时，使用`pnpm add -D`而非`npm install --save-dev`
- 执行脚本命令时，使用`pnpm run`或直接`pnpm`命令，而非`npm run`
- 更新依赖时，使用`pnpm update`而非`npm update`
- 初始化项目时，使用`pnpm init`而非`npm init`
- 全局安装包时，使用`pnpm add -g`而非`npm install -g`
- 执行批量脚本时，优先使用pnpm提供的过滤和并行能力
- 在CI/CD环境中，保持使用pnpm以确保环境一致性
- 若必须使用npm特定命令，应在注释中说明原因
- 如果需要重新运行应用程序，给出提示即可，不用自动重新运行应用程序

## 示例

<example>
// 正确：使用pnpm安装项目依赖
pnpm install

// 正确：添加新依赖
pnpm add axios

// 正确：添加开发依赖
pnpm add -D typescript

// 正确：运行开发服务器
pnpm dev

// 正确：运行构建命令
pnpm build

// 正确：执行自定义脚本
pnpm run lint
</example>

<example type="invalid">
// 错误：使用npm安装依赖
npm install

// 错误：使用npm添加依赖
npm install axios

// 错误：使用npm添加开发依赖
npm install --save-dev typescript

// 错误：使用npm运行脚本
npm run dev

// 错误：使用npm执行构建
npm run build
</example>