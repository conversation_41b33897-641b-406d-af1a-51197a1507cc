---
description: 
globs: 
alwaysApply: true
---
---
description: 此规则规定了Vue组件的组织结构和代码放置位置。应在以下情况应用：(1)创建新组件，(2)重构现有组件，(3)移动或重组组件，(4)评估代码结构。此规则确保代码的可维护性、可重用性和组织清晰度，对于大型应用的代码管理和团队协作至关重要。
globs: 
alwaysApply: false
---

# 组件组织结构规则

## 关键规则

- 所有公共组件（可在多个功能模块中复用的组件）必须放置在 `src/components` 目录中
- 子模块公共组件放在 `src/submodule/ent-module/components` 目录中
- 页面特定的组件（仅在单个页面中使用的组件）必须放置在对应路由目录下的 `components` 文件夹中，如 `src/routes/device/manage/components/`
- 组件名称应使用 PascalCase 命名规范（如 `DeviceCard.vue`）
- 路由组件不应直接导入其他路由的特定组件，应将共享组件提升为公共组件
- 组件目录结构应反映其用途和相关性，相关组件应放在同一子目录中
- 功能模块的组件应放置在对应的routes目录下，而非views目录
- 微前端子应用的共享组件应放在submodule目录下
- 按照Vue组件形式进行封装
- 封装的组件具有通用性，拓展性强

## 示例

<example>
  ✅ 正确的组件组织:
  
  src/components/Button.vue  // 全局公共按钮组件
  src/components/forms/FormField.vue  // 全局公共表单组件
  
  src/submodule/ent-module/components/ui-filter/ui-select-panel/index.vue  // 子模块公共组件
  
  src/routes/device/manage/components/DeviceCard.vue  // 设备管理路由特定组件
  src/routes/user/profile/components/AvatarUploader.vue  // 用户资料路由特定组件
</example>

<example type="invalid">
  ❌ 错误的组件组织:
  
  src/routes/common/Button.vue  // 错误：公共组件不应放在routes目录
  src/components/device/DeviceCard.vue  // 错误：路由特定组件不应放在公共components目录
  
  src/routes/device/DeviceForm.vue  // 错误：路由特定组件应放在components子目录
  src/views/device/manage/DeviceList.vue  // 错误：不应使用views目录，应使用routes目录
  
  src/components/ui-select-panel/index.vue  // 错误：子模块组件应放在submodule目录下
</example>
