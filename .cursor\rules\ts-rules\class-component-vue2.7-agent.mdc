---
description:
globs:
alwaysApply: false
---
---
description: 此规则适用于在Vue 2.7项目中使用Class Component的场景。应在以下情况应用：(1)创建新的基于类的Vue组件，(2)修改现有的类组件，(3)维护使用了vue-class-component和vue-property-decorator的代码。此规则确保类组件的一致性和类型安全，特别适用于从Angular或其他基于类的框架迁移过来的团队。
globs: 
alwaysApply: false
---

# Vue 2.7 Class Component 开发规范

## 关键规则

- 使用`vue-class-component`和`vue-property-decorator`来实现基于类的组件
- 组件类必须继承自`Vue`并使用`@Component`装饰器
- 属性使用`@Prop()`装饰器定义，并明确指定类型和默认值
- 监听属性使用`@Watch()`装饰器，指定监听的属性名和选项
- 组件方法不使用箭头函数，以确保`this`指向组件实例
- 计算属性使用`get`访问器定义，不使用`@Computed`装饰器
- 事件发射使用`this.$emit()`方法
- 使用`@Emit()`装饰器可自动发射同名事件
- 引用DOM元素使用`@Ref()`装饰器
- 使用`@Mixins()`装饰器引入混合
- TypeScript接口用于定义Props和数据结构
- 私有方法和属性使用`private`修饰符，公共API使用`public`

## 示例

<example>
正确的Vue 2.7 Class Component用法:

```vue
<template>
  <div>
    <h2>{{ title }}</h2>
    <p>Count: {{ count }}</p>
    <button @click="increment">Increment</button>
    <input ref="nameInput" v-model="userName" />
    <button @click="saveUser">Save User</button>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Emit, Ref } from 'vue-property-decorator'
import UserService from '@/services/UserService'

// 用于Props的接口
interface UserProps {
  id: number
  name: string
}

@Component({
  components: {
    // 子组件注册
  }
})
export default class UserCounter extends Vue {
  // Props定义
  @Prop({ type: Number, default: 0 }) 
  private readonly initialCount!: number

  @Prop({ 
    type: Object, 
    required: true,
    validator: (prop: UserProps) => !!prop.id
  }) 
  private readonly user!: UserProps

  // 数据属性
  private count: number = 0
  private userName: string = ''

  // Ref引用
  @Ref('nameInput') 
  private nameInput!: HTMLInputElement

  // 计算属性
  get doubleCount(): number {
    return this.count * 2
  }

  // 侦听器
  @Watch('count')
  private onCountChange(newValue: number, oldValue: number): void {
    console.log(`Count changed from ${oldValue} to ${newValue}`)
  }

  // 生命周期钩子
  created(): void {
    this.count = this.initialCount
    this.userName = this.user.name
  }

  mounted(): void {
    this.nameInput.focus()
  }

  // 方法
  private increment(): void {
    this.count++
    this.$emit('increment', this.count)
  }

  // 带事件发射的方法
  @Emit('save')
  private saveUser(): UserProps {
    return {
      id: this.user.id,
      name: this.userName
    }
  }

  // 异步方法示例
  private async fetchUserData(): Promise<void> {
    try {
      const userData = await UserService.getUserById(this.user.id)
      this.userName = userData.name
    } catch (error) {
      console.error('Failed to fetch user data', error)
    }
  }
}
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```
</example>

<example type="invalid">
错误的Vue 2.7 Class Component用法:

```vue
<template>
  <div>
    <p>{{ count }}</p>
    <button @click="increment">+</button>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'

// 错误: 缺少类型注解和访问修饰符
@Component
export default class Counter extends Vue {
  count = 0  // 错误: 没有访问修饰符和类型注解
  
  // 错误: 使用箭头函数，可能导致this绑定问题
  increment = () => {
    this.count++
  }
  
  // 错误: 生命周期钩子作为箭头函数
  mounted = () => {
    console.log(this.count)
  }
  
  // 错误: 计算属性没有使用get访问器
  doubleCount() {
    return this.count * 2
  }
  
  // 错误: 未定义类型的参数
  updateCount(value) {
    this.count = value
  }
}
</script>

<script lang="ts">
// 错误: 混合装饰器和选项API
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component({
  data() {  // 错误: 与类组件模式混用选项API
    return {
      message: 'Hello'
    }
  }
})
export default class MixedComponent extends Vue {
  @Prop() title!: string  // 错误: 缺少类型定义和可选性标记
  
  // ...其他代码
}
</script>
```
</example>
