# 1. API 版本声明
apiVersion: networking.k8s.io/v1 # 使用 Kubernetes 官方 Ingress API 的 v1 版本
kind: Ingress # 资源类型为 Ingress

# 2. 元数据部分
metadata:
  # Ingress 资源名称，由三部分组成：
  # - __CI_PROJECT_NAME__: 项目名称（如 ent-paas）
  # - __CI_ENVIRONMENT_SLUG__: 环境标识（如 prod, sit）
  # - ingress: 资源类型标识
  name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__-ingress

  # 部署的目标命名空间
  # __CUST_APP_NAMESPACE__: 应用所在的命名空间（如 webb）
  namespace: __CUST_APP_NAMESPACE__

# 3. 详细配置部分
spec:
  # 指定使用的 Ingress 控制器类
  ingressClassName: alb-ingress-class # 使用 AWS ALB Ingress Controller

  # 流量路由规则配置
  rules:
    # 第一条路由规则
    - host: __APP_URL__ # 匹配的域名（如 paas.qixin.com）
      http:
        paths:
          # 路径配置
          - path: / # 匹配根路径
            pathType: Prefix # 路径匹配类型为前缀匹配
            backend:
              # 后端服务配置
              service:
                # 目标服务名称，格式：项目名-环境标识
                name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
                port:
                  number: 80 # 服务端口
