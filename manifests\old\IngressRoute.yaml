---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__-route
  # 修改为规划 Namespaces
  namespace: __CUST_APP_NAMESPACE__
spec:
  entryPoints:
    - web
  routes:
    # 此处为服务域名配置位置，可更新为变量，端口禁止改动，本路由文件原则上禁止改动，若需增加相关规则，请提前通知
    - match: Host(`__APP_URL__`)
      kind: Rule
      services:
        - name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
          port: 80
# # apiVersion: apisix.apache.org/v2
# # kind: ApisixRoute
# # metadata:
# #   name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__-route
# #   namespace: __CUST_APP_NAMESPACE__
# # spec:
# #   http:
# #     - backends:
# #         - serviceName: web-uat
# #           servicePort: 80
# #       match:
# #         hosts:
# #           - __APP_URL__
# #         paths:
# #           - /*
# #       name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
# # ---
# apiVersion: traefik.containo.us/v1alpha1
# kind: IngressRoute
# metadata:
#   name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__-route
#   # 修改为规划 Namespaces
#   namespace: __CUST_APP_NAMESPACE__
# spec:
#   entryPoints:
#     - web
#   routes:
#     # 此处为服务域名配置位置，可更新为变量，端口禁止改动，本路由文件原则上禁止改动，若需增加相关规则，请提前通知
#     - match: Host(`__APP_URL__`)
#       kind: Rule
#       services:
#         - name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
#           port: 80
