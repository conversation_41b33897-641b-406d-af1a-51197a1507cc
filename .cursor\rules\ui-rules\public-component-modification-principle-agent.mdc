---
description: 公共组件修改原则
globs: 
alwaysApply: false
---
# 公共组件修改原则

## 关键规则

- 公共组件（如 `src/components/` 下的组件）原则上不应为了满足特定业务场景而被修改。
- 当特定页面或功能需要调整公共组件的行为或样式时，应优先考虑在调用方进行适配。
- 适配方式可以包括：通过`props`传递不同的配置、使用`slots`定制内容、或在调用方用一个新组件包装该公共组件以添加或覆盖逻辑。
- 只有当修改是通用性的增强或缺陷修复，且对所有使用方都有益时，才应直接修改公共组件。
- 在修改公共组件前，必须评估其对所有引用处的影响，确保向后兼容性或进行全局同步更新。

## 示例

<example>
  ✅ 正确的原则应用:

  **场景**: 一个特定页面 (`src/routes/special-promo/index.vue`) 需要将全局公共的 `AppButton` 组件背景色变为红色，而该组件默认是蓝色。

  **解决方案**: 在调用方进行样式覆盖或传递属性，而不是修改公共组件本身。

  ```vue
  <!-- src/routes/special-promo/index.vue -->
  <template>
    <div>
      <h1>Special Promotion</h1>
      <!-- 正确：通过 prop 或 class 在调用方覆盖样式 -->
      <app-button
        class="buy-now-button"
        @click="handleBuy"
      >
        Buy Now
      </app-button>
    </div>
  </template>

  <script setup>
  import AppButton from '@/components/app-button/index.vue';

  function handleBuy() {
    // ...
  }
  </script>

  <style scoped>
  .buy-now-button {
    background-color: red !important; /* 或者使用 unocss/tailwindcss 原子类 */
    border-color: red !important;
  }
  </style>
  ```

  ```vue
  <!-- src/components/app-button/index.vue -->
  <!-- 公共组件保持不变，维持其通用性 -->
  <template>
    <button class="app-button">
      <slot></slot>
    </button>
  </template>

  <style scoped>
  .app-button {
    background-color: blue;
    color: white;
    /* ... other general styles */
  }
  </style>
  ```
</example>

<example type="invalid">
  ❌ 错误的原则应用:

  **场景**: 同上。

  **错误做法**: 为了满足单个页面的需求，直接修改了公共组件的源文件。

  ```vue
  <!-- src/components/app-button/index.vue -->
  <!-- 错误：为了单个页面的需求，直接修改了公共组件 -->
  <template>
    <button class="app-button">
      <slot></slot>
    </button>
  </template>

  <style scoped>
  .app-button {
    /* 错误地将通用组件的默认样式改为了红色，会影响所有使用该组件的地方 */
    background-color: red; 
    color: white;
    /* ... other general styles */
  }
  </style>
  ```
  **后果**: 此修改会导致整个应用中所有使用 `<app-button>` 的地方按钮都变成红色，引发非预期的全局UI变更。
</example>
