---
description: 按需引入与动态加载规则
globs: 
alwaysApply: false
---
# 按需引入与动态加载规则

## 关键规则

- 优先使用动态导入 (`import()`) 来加载非首屏或非核心的组件、库或模块。
- 避免在主包 (main bundle) 中静态导入 (`import ... from ...`) 大型或不常用的依赖。
- 对于Vue组件，特别是弹窗、非立即显示的Tab内容等，应使用异步组件或动态 `import()` 方式加载。
- 在路由配置中，对页面/视图组件使用动态导入（懒加载）。
- 对于大型库，如果其提供了按需加载的方法，应优先使用。

## 示例

<example>
  ✅ 正确的按需引入:
  
  // 1. 动态导入Vue组件（例如，一个弹窗）
  ```javascript
  import { showPureDialog } from '@/core/ui-service/index.js'

  function openMyDialog() {
    showPureDialog(() => import('./components/MyHeavyDialog.vue'), {
      // ...props
    })
  }
  ```

  // 2. 在Vue Router中懒加载路由组件
  ```javascript
  const routes = [
    {
      path: '/my-page',
      name: 'MyPage',
      component: () => import('@/views/MyPageView.vue') // 懒加载
    }
  ]
  ```

  // 3. 动态导入一个不常用的库
  ```javascript
  async function useHeavyLibrary() {
    const { someFunction } = await import('heavy-library');
    someFunction();
  }
  ```
</example>

<example type="invalid">
  ❌ 错误的静态引入:
  
  // 1. 静态导入一个不常用的重型组件
  ```javascript
  import MyHeavyDialog from './components/MyHeavyDialog.vue'; // 错误：会打包进主文件
  import { showPureDialog } from '@/core/ui-service/index.js'

  function openMyDialog() {
    showPureDialog(MyHeavyDialog, { // 即使在这里使用，组件也已经被静态加载了
      // ...props
    })
  }
  ```

  // 2. 在Vue Router中静态导入组件
  ```javascript
  import MyPageView from '@/views/MyPageView.vue'; // 错误：未使用懒加载

  const routes = [
    {
      path: '/my-page',
      name: 'MyPage',
      component: MyPageView 
    }
  ]
  ```
</example>
