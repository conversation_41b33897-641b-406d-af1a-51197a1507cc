#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 仅检查指定子模块的commit变更
SUB_MODULE_PATH="src/submodule/ent-module"
SUB_DIFF=$(git diff --cached --submodule=diff -- "$SUB_MODULE_PATH")
CURRENT_BRANCH=$(git branch --show-current)

if [ -n "$SUB_DIFF" ]; then
  # 检查分支是否允许
  case "$CURRENT_BRANCH" in
  DEV* | UAT* | master*) ;;
  *)
    echo "🐾 检测到 ent-module 的commit变动喵！！🐾 温馨提示：子模块需要单独提交哦～"
    exit 1
    ;;
  esac
fi
