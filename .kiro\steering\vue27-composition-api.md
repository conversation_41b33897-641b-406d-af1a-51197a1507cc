---
inclusion: fileMatch
fileMatchPattern: '**/*.vue'
---

# Vue 2.7 Composition API 开发规范

## 关键规则

- 使用`<script lang="ts">` + `setup()` 函数实现组合式 API，而非 Vue 3 的`<script setup>`
- 所有从`setup()`返回的数据和方法必须显式声明类型
- 使用`defineComponent`包装组件以获得更好的类型推导
- Props 类型定义使用 PropType，如`type: Object as PropType<User>`
- 使用`ref<T>()`, `reactive<T>()` 等明确指定响应式数据的类型
- 生命周期钩子映射：使用 `onMounted`, `onUnmounted` 等替代选项式 API 中的生命周期方法
- 使用 TypeScript 接口定义复杂数据结构和组件 props
- 组件事件使用`context.emit('eventName', payload)`方式触发
- 采用`useXxx`命名约定创建可重用的组合式函数
- 使用`computed<T>(() => {})` 为计算属性提供类型
- 暴露给父组件的方法和属性通过显式的 return 语句返回
- 复杂业务逻辑抽取到`/composables`或组件相关目录下的组合式函数中

## 正确的 Vue 2.7 Composition API 用法

```vue
<template>
  <div>
    <p>Count: {{ count }}</p>
    <button @click="increment">Increment</button>
    <div>
      <h3>User Info</h3>
      <input ref="nameInput" v-model="user.name" placeholder="用户名" />
      <p>Email: {{ user.email }}</p>
      <button @click="selectUser">选择用户</button>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, computed, onMounted, watch, PropType } from 'vue'

  // 类型定义
  interface User {
    id: number
    name: string
    email: string
  }

  export default defineComponent({
    name: 'UserCounter',

    props: {
      initialCount: {
        type: Number,
        default: 0
      },
      userData: {
        type: Object as PropType<User>,
        default: () => ({ id: 0, name: '', email: '' })
      }
    },

    emits: ['increment', 'userSelected'],

    setup(props, { emit, refs }) {
      // 响应式状态
      const count = ref<number>(props.initialCount)
      const user = reactive<User>({ ...props.userData })
      const nameInput = ref<HTMLInputElement | null>(null)

      // 计算属性
      const doubleCount = computed<number>(() => count.value * 2)

      // 侦听器
      watch(count, (newValue, oldValue) => {
        console.log(`Count changed from ${oldValue} to ${newValue}`)
      })

      // 生命周期钩子
      onMounted(() => {
        if (nameInput.value) {
          nameInput.value.focus()
        }
      })

      // 方法
      const increment = (): void => {
        count.value++
        emit('increment', count.value)
      }

      const selectUser = (): void => {
        emit('userSelected', user)
      }

      // 暴露给模板
      return {
        count,
        doubleCount,
        user,
        nameInput,
        increment,
        selectUser
      }
    }
  })
</script>

<style lang="scss" scoped>
  /* 组件样式 */
</style>
```

## 可复用的组合式函数示例

```typescript
// src/composables/useDeviceData.ts
import { ref, computed, onMounted, Ref, ComputedRef } from 'vue'
import { fetchDevices, DeviceInfo } from '@/api/devices'

interface UseDeviceDataReturn {
  devices: Ref<DeviceInfo[]>
  activeDevices: ComputedRef<DeviceInfo[]>
  loading: Ref<boolean>
  error: Ref<Error | null>
  loadDevices: () => Promise<void>
}

export function useDeviceData(limit: number = 10): UseDeviceDataReturn {
  const devices = ref<DeviceInfo[]>([])
  const loading = ref<boolean>(true)
  const error = ref<Error | null>(null)

  const activeDevices = computed<DeviceInfo[]>(() => devices.value.filter(device => device.status === 'active'))

  const loadDevices = async (): Promise<void> => {
    loading.value = true
    try {
      devices.value = await fetchDevices(limit)
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    loadDevices()
  })

  return {
    devices,
    activeDevices,
    loading,
    error,
    loadDevices
  }
}
```
