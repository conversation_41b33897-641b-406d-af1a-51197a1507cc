# ent-web 项目 Webpack 性能优化指南

## 引言

随着 `ent-web` 项目功能的日益复杂和代码量的持续增长，Webpack 的构建性能（包括构建速度和产物体积）成为影响开发效率和用户体验的关键因素。构建速度过慢会延长开发人
员的等待时间，影响迭代效率；产物体积过大会增加用户的首屏加载时间，降低应用性能。

本文档严格参考业界优秀的 Webpack 性能优化实践，结合 `ent-web` 项目的现有技术栈（Webpack 5, Vue 2.7, Babel），旨在提供一套系统、可落地的性能优化方案，帮助我们打
造更快速、更轻量的应用。

---

## 一、构建速度优化

提升构建速度的核心思路是：**减少重复计算、缩小搜索范围、利用并行处理**。

### 1. 多进程构建与编译

**问题**：Webpack 是单线程的，当需要处理大量模块时，JavaScript 的计算密集型任务（如 Babel 转译、TypeScript 编译）会成为构建速度的瓶颈。

**方案**：

- **`thread-loader`**: 将耗时的 Loader（如 `babel-loader`）分配给多个 Worker 线程并行处理。
- **`ts-loader` 编译加速**: 通过设置 `transpileOnly: true`，让 `ts-loader` 只负责代码转换而不进行类型检查，将类型检查任务交由 IDE 或 CI 流程处理，实现职责分离，
  从而大幅提升编译速度。

**配置建议**：

```javascript
// tools/webpack/client.build.js
module.exports = {
  // ...
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: ['thread-loader', 'babel-loader'] // 为 babel-loader 开启多进程
      },
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: [
          'thread-loader', // 为 ts-loader 开启多进程
          'babel-loader',
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true, // 只转换，不检查类型
              appendTsSuffixTo: [/\.vue$/]
            }
          }
        ]
      }
    ]
  }
}
```

### 2. 并行压缩代码

**问题**：在生产环境构建中，代码压缩是另一个非常耗时的步骤。

**方案**：`ent-web` 项目使用的 Webpack 5 内置了 `terser-webpack-plugin`，它默认开启了多进程并行压缩。我们只需确保该配置是启用的即可。

**配置建议**：

```javascript
// tools/webpack/webpack.prod.js
const TerserPlugin = require('terser-webpack-plugin')
module.exports = {
  // ...
  optimization: {
    minimizer: [
      new TerserPlugin({
        parallel: true // 开启多进程并行压缩
      })
    ]
  }
}
```

### 3. 缓存优化

**问题**：每次构建都重新编译所有文件，效率低下。

**方案**：利用缓存，让二次构建只编译修改过的文件。

- **Webpack 5 持久化缓存**：这是 Webpack 5 的重大升级，能将模块、chunk 和 asset 缓存到文件系统，是提升二次构建速度最有效的手段。
- **关于 DLL 的演进**：分析项目可知，旧有的 DLL (动态链接库) 方案 (`dll.conf.js`) 已被废弃。这是合理的工程演进，因为 Webpack 5 强大的持久化缓存机制在多数场景下
  已完全替代了 DLL 的功能，且配置更简单、效果更优。

**配置建议**：

```javascript
// tools/webpack/webpack.base.js
module.exports = {
  // ...
  cache: {
    type: 'filesystem', // 使用文件系统缓存
    cacheDirectory: path.resolve(__dirname, '../../.temp_cache'), // 缓存目录
    buildDependencies: {
      config: [__filename] // 当配置变化时，缓存失效
    }
  }
}
```

### 4. 缩小构建目标 (减少文件搜索范围)

**问题**：Webpack 在构建时需要解析大量文件，不必要的搜索和编译会浪费时间。

**方案**：通过优化 `resolve` 配置和限定 `loader` 范围，减少文件搜索。

- **`exclude` / `include`**: 明确告知 `loader` 不需要处理哪些文件，特别是 `node_modules`。
- **`resolve.alias`**: 为常用模块创建别名，避免 Webpack 逐层查找。
- **`resolve.extensions`**: 减少不必要的后缀名尝试。

**配置建议**：

```javascript
// tools/webpack/webpack.base.js
const path = require('path')
module.exports = {
  // ...
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../../src'),
      vue$: 'vue/dist/vue.esm.js'
    },
    extensions: ['.js', '.vue', '.json', '.ts']
  }
}
```

### 5. TypeScript 编译加速

**问题**：TypeScript 的类型检查是一个耗时的过程，会显著影响构建速度。

**方案**：为了提升 TypeScript 的编译速度，项目在 `ts-loader` 的配置中启用了 `transpileOnly: true` 选项。

**原理**：该选项使得 `ts-loader` 只负责将 TypeScript 代码转换为 JavaScript，而**跳过类型检查**。类型检查的任务被分离出去，通常由 IDE（如 VSCode）实时进行，或通
过在 CI/CD 流程中执行 `tsc --noEmit` 命令来保证。

**优势**：职责分离，构建时只关注转换，显著提升了构建效率。

**配置建议**：

```javascript
// tools/webpack/client.build.js
{
  test: /\.ts$/,
  exclude: /node_modules/,
  use: [
    'thread-loader', // 多进程处理
    'babel-loader',
    {
      loader: 'ts-loader',
      options: {
        transpileOnly: true, // 只转换，不进行类型检查
        appendTsSuffixTo: [/\.vue$/]
      }
    }
  ]
}
```

### 6. 关于 DLL (动态链接库) 的演进说明

**历史背景**：通过分析 `tools/webpack/dll.conf.js` 文件，我们发现该文件头部明确注释 **"该文件已废弃，仅做参考"**。

这表明 `ent-web` 项目在早期可能采用过 `webpack.DllPlugin` 来预编译第三方库以提升开发环境的构建速度，但后续已放弃该方案。

**演进原因**：

- **Webpack 5 缓存机制的成熟**：Webpack 5 强大的持久化文件系统缓存 (`cache: { type: 'filesystem' }`) 在很大程度上替代了 DLL 的功能，且配置更简单、效果更优。
- **维护成本**：DLL 方案需要手动维护要预编译的库列表，并且在库版本更新后需要重新构建 DLL 文件，增加了维护成本。

因此，废弃 DLL 并全面转向 Webpack 5 内置的缓存和 `SplitChunks` 策略，是当前项目在工程化上的一个合理演进。

---

## 二、构建体积优化

减小产物体积的核心思路是：**按需加载、剔除死代码、压缩资源**。

### 1. 代码分包与运行时抽离

**问题**：将所有代码打成一个巨大的包，会导致首屏加载缓慢。

**方案**：通过 Webpack 的 `SplitChunksPlugin` 进行精细化的代码分割。

- **基础分割**: 将 `node_modules` 中的第三方库抽离成 `vendor` chunk。
- **精细化分割**: 将一些体积较大且不常变动的库（如 `cytoscape`, `pdfjs-dist`）单独打包，可以实现更有效的按需加载和长期缓存。
- **运行时抽离**: 将 Webpack 的运行时代码 (`runtimeChunk`) 单独抽离，避免因其变化导致 `vendor` chunk 缓存失效。

**配置建议**：

```javascript
// tools/webpack/webpack.prod.js
module.exports = {
  // ...
  optimization: {
    runtimeChunk: { name: () => 'manifest' }, // 抽离运行时
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          // 通用第三方库
          test: /[\\/]node_modules[\\/]/,
          name: 'chunk-vendors',
          priority: -10
        },
        cytoscape: {
          // 单独打包 cytoscape
          name: 'cytoscape',
          test: /[\\/]node_modules[\\/]cytoscape/,
          priority: 10
        }
        // ... 其他大型库的精细化分割
      }
    }
  }
}
```

### 2. Tree Shaking (摇树优化)

**问题**：项目中引入但未使用的代码被打包，增加了体积。

**方案**：Webpack 5 默认在生产模式下开启 Tree Shaking，并对其功能进行了增强，能更有效地移除死代码。我们需要确保满足其生效条件。

- **使用 ES6 模块语法** (`import/export`)。
- **Babel 配置**: 确保 Babel 不会将 ES6 模块转换为 CommonJS 模块 (`modules: false`)。
- **`sideEffects`**: 在 `package.json` 中标记没有副作用的文件（如纯 CSS 文件），告知 Webpack 可以安全地进行 Tree Shaking。

**配置建议**：

```json
// package.json
{
  "name": "ent-web",
  "sideEffects": ["*.css", "*.scss"]
}
```

### 3. 资源压缩与优化

**问题**：高清图片和庞大的字体文件是体积优化的重灾区。

**方案**：

- **原生资源模块**: 使用 Webpack 5 的 Asset Modules (`type: 'asset'`) 替代旧的 `file-loader`/`url-loader`，可以根据资源大小自动选择是生成文件还是内联为 Base64。
- **图片压缩**: 使用 `image-webpack-loader` 在构建时自动压缩图片。

**配置建议**：

```javascript
// tools/webpack/webpack.base.js
module.exports = {
  // ...
  module: {
    rules: [
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset', // 自动选择 asset/resource 或 asset/inline
        parser: {
          dataUrlCondition: { maxSize: 10 * 1024 } // 小于10KB内联
        },
        use: [
          {
            // image-webpack-loader 需要放在 use 中
            loader: 'image-webpack-loader',
            options: {
              mozjpeg: { quality: 65 }
            }
          }
        ]
      }
    ]
  }
}
```

### 4. 删除无用代码

**问题**：除了 JS，项目中还可能存在大量未使用的 CSS 规则或第三方库中不必要的模块。

**方案**：

- **CSS Tree Shaking**: 使用 `purgecss-webpack-plugin` 结合 `glob` 来分析模板和 JS 文件，移除未被引用的 CSS。
- **库模块裁剪**: 针对像 `Moment.js` 这样包含大量本地化文件的库，使用 `moment-locales-webpack-plugin` 只保留需要的语言包。

**配置建议**：

```javascript
// tools/webpack/webpack.prod.js
const PurgecssPlugin = require('purgecss-webpack-plugin')
const MomentLocalesPlugin = require('moment-locales-webpack-plugin')

module.exports = {
  // ...
  plugins: [
    new PurgecssPlugin({
      paths: glob.sync(`${PATHS.src}/**/*.{vue,js,ts}`, { nodir: true })
    }),
    new MomentLocalesPlugin({
      localesToKeep: ['zh-cn'] // 只保留中文语言包
    })
  ]
}
```

### 5. 静态资源处理

### 6. 精细化的代码分割 (Advanced SplitChunks)

**问题**：将所有第三方库打成一个巨大的 vendor.js，会导致缓存粒度过粗，任何一个库的更新都会使整个 vendor 缓存失效。

**方案**：除了对 `node_modules` 下的模块进行统一抽离（`chunk-vendors`），项目还对一些体积较大且不常变动的第三方库（如 `html2canvas`, `cytoscape`, `pdfjs-dist`）
设置了独立的 `cacheGroups`。

**优势**：

- **隔离变化**：将这些大型库独立打包，可以避免它们因为其他小模块的变动而导致整个 `vendor` chunk 的缓存失效。
- **更优的按需加载**：当某个页面需要用到 `cytoscape` 时，浏览器只需加载这个特定的 `cytoscape.js` chunk，而不是一个包含大量其他库的、臃肿的 `vendor.js`。

**配置建议**：

```javascript
// tools/webpack/client.build.js
splitChunks: {
  chunks: 'all',
  cacheGroups: {
    vendor: {
      test: /[\\/]node_modules[\\/]/,
      name: 'chunk-vendors',
      priority: -10
    },
    html2canvas: {
      name: 'chunk-html2canvas',
      test: /[\\/]node_modules[\\/]html2canvas[\\/]/,
      chunks: 'all',
      priority: 10 // 优先级高于 vendor
    },
    cytoscape: {
      name: 'cytoscape',
      test: /[\\/]node_modules[\\/]cytoscape/,
      chunks: 'all',
      priority: 10
    },
    pdfjsDist: {
      name: 'pdfjs-dist',
      test: /[\\/]node_modules[\\/]pdfjs-dist[\\/]/,
      chunks: 'all',
      priority: 10
    }
  }
}
```

### 7. Moment.js 语言包裁剪

**问题**：`Moment.js` 库默认包含了全球所有语言的本地化文件，体积非常庞大。

**方案**：项目引入了 `moment-locales-webpack-plugin`，在构建时仅保留中文（`zh-cn`）语言包，移除了所有其他不需要的本地化文件。

**效果**：这是一个非常有效的体积优化，可以为项目减去数百 KB 的大小。

**配置建议**：

```javascript
// tools/webpack/client.build.js
const MomentLocalesPlugin = require('moment-locales-webpack-plugin')

plugins: [
  new MomentLocalesPlugin({
    localesToKeep: ['zh-cn']
  })
]
```

### 8. 运行时代码抽离 (runtimeChunk)

**问题**：Webpack 的运行时代码会影响 vendor chunk 的缓存稳定性。

**方案**：项目配置了 `optimization.runtimeChunk`，将 Webpack 用于连接模块的运行时代码（runtime）和模块清单（manifest）抽离成一个独立的 `manifest.js` 文件。

**优势**：

- **长期缓存优化**：`vendor` chunk 通常包含不常变化的第三方库。如果没有抽离 `runtimeChunk`，每次构建即使只有业务代码变化，`runtime` 的变化也会导致 `vendor`
  chunk 的 `contenthash` 改变，使其缓存失效。抽离后，只要第三方库不变，`vendor` chunk 就可以被浏览器长期缓存。

**配置建议**：

```javascript
// tools/webpack/client.build.js
optimization: {
  runtimeChunk: {
    name: () => 'manifest'
  }
}
```

### 9. 原生资源模块 (Asset Modules)

**问题**：在 Webpack 4 及更早版本中，处理图片、字体等静态资源需要依赖 `file-loader`、`url-loader`、`raw-loader` 等一系列 loader。

**方案**：Webpack 5 引入了原生的资源模块（Asset Modules），通过四种新的模块类型，无需额外配置 loader 即可处理任何静态资源。`ent-web` 项目中已采用
`type: 'asset/resource'` 来处理图片和字体文件。

- **`asset/resource`**: 发送一个单独的文件并导出 URL。取代 `file-loader`。
- **`asset/inline`**: 导出一个资源的 data URI。取代 `url-loader`。
- **`asset/source`**: 导出资源的源代码。取代 `raw-loader`。
- **`asset`**: 在 `asset/resource` 和 `asset/inline` 之间自动选择，默认阈值为 8KB（可通过 `parser.dataUrlCondition.maxSize` 修改）。

**配置建议**：

```javascript
// tools/webpack/client.build.js
{
  test: /\.(ico|gif|png|jpg|jpeg|webp)$/i,
  type: 'asset/resource' // 使用 asset/resource 类型处理图片
},
{
  test: /\.(woff2?|ttf|eot|svg)(\?[\s\S])?$/,
  type: 'asset/resource' // 使用 asset/resource 类型处理字体
}
```

### 10. 确定性的 ID 生成与长期缓存

**问题**：在旧版 Webpack 中，模块 ID 默认是基于解析顺序的数字。当添加或删除模块时，可能导致所有后续模块的 ID 发生变化，进而使得大量文件的 `contenthash` 改变，导
致浏览器缓存大面积失效。

**方案**：项目在生产环境配置中启用了 `optimization.moduleIds: 'deterministic'`。此选项会为模块生成基于其路径的、简短且稳定的 hash ID。

**优势**：

- **缓存稳定性**：只要模块内容不变，其 ID 就不会变，从而保证了不相关模块的 `contenthash` 的稳定性，最大化地利用了长期缓存。
- 在 Webpack 5 中，`chunkIds: 'deterministic'` 和 `moduleIds: 'deterministic'` 在生产模式下是默认开启的，项目中的显式配置确保了这一最佳实践的执行。

**配置建议**：

```javascript
// tools/webpack/client.build.js
optimization: {
  moduleIds: 'deterministic'
}
```

### 11. 增强的 Tree Shaking

**背景**：Webpack 5 在 Tree Shaking 方面取得了显著进步，使得死代码剔除更加彻底和智能。

**增强特性**：

- **嵌套属性摇树**：现在可以摇掉嵌套模块中未使用的属性。
- **CommonJS 摇树**：对一些常见的 CommonJS 模块格式提供了实验性的支持，能够分析其导出和依赖关系，从而移除未使用的 CommonJS 模块。
- **副作用分析更精确**：通过 `package.json` 的 `sideEffects` 标志，可以更精确地识别纯模块，确保安全的摇树操作。

**实践要点**：`ent-web` 项目通过遵循 ES6 模块规范和正确配置 `sideEffects`，已经充分利用了 Webpack 5 增强的 Tree Shaking 能力，有效减小了最终的包体积。

---

## 三、高级实践与分析

### 1. 构建性能分析

**工具**：

- **速度分析**: `speed-measure-webpack-plugin`，可以详细输出每个 Loader 和 Plugin 的执行耗时。
- **体积分析**: `webpack-bundle-analyzer`，可以生成可视化的分析报告，直观展示每个模块的体积占比。

**使用方法**：

```javascript
// 速度分析
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')
const smp = new SpeedMeasurePlugin()
module.exports = smp.wrap({
  /* ... 你的webpack配置 */
})

// 体积分析
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
module.exports = {
  plugins: [new BundleAnalyzerPlugin()]
}
```

### 2. Service Worker 与离线缓存

**方案**：项目通过 `workbox-webpack-plugin` 集成了 Service Worker，实现了资源的离线缓存。

**优势**：

- **二次加载加速**：用户在第二次访问应用时，静态资源（JS, CSS, 图片等）可以直接从 Service Worker 的缓存中读取，大大加快了加载速度。
- **离线访问**：为应用提供了基础的离线运行能力。

**配置示例**：

```javascript
// tools/webpack/client.build.js
const WorkboxPlugin = require('workbox-webpack-plugin')

plugins: [
  new WorkboxPlugin.InjectManifest({
    swSrc: './src/sw.js',
    swDest: 'service-worker.js'
  })
]
```

### 3. Public 目录与静态资源处理

**工作原理**：项目中的 `public` 目录用于存放不会被 Webpack 处理的静态资源。

- **直接复制**：在构建过程中，`public` 目录下的所有文件会被原封不动地复制到最终的输出目录（`dist/public`）。这适用于那些无需编译、路径固定的文件，如
  `favicon.ico` 或一些第三方库。
- **模板注入**：`HtmlWebpackPlugin` 会使用 `public/index.html`（或 `src/index.html`）作为模板。在构建时，它会自动将打包好的 JS 和 CSS 文件的引用（`<script>` 和
  `<link>` 标签）注入到这个 HTML 文件中，生成最终的入口 HTML。

**设计优势**：这种机制清晰地分离了需要 Webpack 打包处理的源码（在 `src` 中）和仅需静态服务的资源（在 `public` 中）。

---

## 四、总结与建议

针对 `ent-web` 项目，建议成立一个专项优化小组，按以下步骤实施性能优化：

| **优化方向** | **关键技术/工具**                       | **预期效果**                             | **优先级** |
| ------------ | --------------------------------------- | ---------------------------------------- | ---------- |
| **构建速度** | Webpack 5 `cache`, `thread-loader`      | 二次构建速度提升 70%+, 首次构建提升 20%+ | **高**     |
| **构建体积** | `SplitChunks`, `PurgeCSS`, Tree Shaking | 首屏关键资源体积减少 20%-40%             | **高**     |
| **资源优化** | Asset Modules, `image-webpack-loader`   | 图片资源体积平均减少 30%                 | **中**     |
| **持续监控** | `webpack-bundle-analyzer`               | 建立常规分析机制，防止体积劣化           | **高**     |

## 通过以上系统性的优化措施，`ent-web` 项目的开发体验和线上性能将得到显著提升。
