---
inclusion: fileMatch
fileMatchPattern: '**/*.vue'
---

# Vue 2.7 Class Component 开发规范

## 关键规则

- 使用`vue-class-component`和`vue-property-decorator`来实现基于类的组件
- 组件类必须继承自`Vue`并使用`@Component`装饰器
- 属性使用`@Prop()`装饰器定义，并明确指定类型和默认值
- 监听属性使用`@Watch()`装饰器，指定监听的属性名和选项
- 组件方法不使用箭头函数，以确保`this`指向组件实例
- 计算属性使用`get`访问器定义，不使用`@Computed`装饰器
- 事件发射使用`this.$emit()`方法
- 使用`@Emit()`装饰器可自动发射同名事件
- 引用 DOM 元素使用`@Ref()`装饰器
- 使用`@Mixins()`装饰器引入混合
- TypeScript 接口用于定义 Props 和数据结构
- 私有方法和属性使用`private`修饰符，公共 API 使用`public`

## 正确的 Vue 2.7 Class Component 用法

```vue
<template>
  <div>
    <h2>{{ title }}</h2>
    <p>Count: {{ count }}</p>
    <button @click="increment">Increment</button>
    <input ref="nameInput" v-model="userName" />
    <button @click="saveUser">Save User</button>
  </div>
</template>

<script lang="ts">
  import { Vue, Component, Prop, Watch, Emit, Ref } from 'vue-property-decorator'
  import UserService from '@/services/UserService'

  // 用于Props的接口
  interface UserProps {
    id: number
    name: string
  }

  @Component({
    components: {
      // 子组件注册
    }
  })
  export default class UserCounter extends Vue {
    // Props定义
    @Prop({ type: Number, default: 0 })
    private readonly initialCount!: number

    @Prop({
      type: Object,
      required: true,
      validator: (prop: UserProps) => !!prop.id
    })
    private readonly user!: UserProps

    // 数据属性
    private count: number = 0
    private userName: string = ''

    // Ref引用
    @Ref('nameInput')
    private nameInput!: HTMLInputElement

    // 计算属性
    get doubleCount(): number {
      return this.count * 2
    }

    // 侦听器
    @Watch('count')
    private onCountChange(newValue: number, oldValue: number): void {
      console.log(`Count changed from ${oldValue} to ${newValue}`)
    }

    // 生命周期钩子
    created(): void {
      this.count = this.initialCount
      this.userName = this.user.name
    }

    mounted(): void {
      this.nameInput.focus()
    }

    // 方法
    private increment(): void {
      this.count++
      this.$emit('increment', this.count)
    }

    // 带事件发射的方法
    @Emit('save')
    private saveUser(): UserProps {
      return {
        id: this.user.id,
        name: this.userName
      }
    }

    // 异步方法示例
    private async fetchUserData(): Promise<void> {
      try {
        const userData = await UserService.getUserById(this.user.id)
        this.userName = userData.name
      } catch (error) {
        console.error('Failed to fetch user data', error)
      }
    }
  }
</script>

<style lang="scss" scoped>
  /* 组件样式 */
</style>
```
