apiVersion: apps/v1
kind: Deployment
metadata:
  name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
  namespace: __CUST_APP_NAMESPACE__
  labels:
    app: __CUST_APP_NAME__
    ref: __CI_ENVIRONMENT_SLUG__
spec:
  replicas: __CUST_REPLICAS__
  minReadySeconds: 10
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 0
  selector:
    matchLabels:
      app: __CUST_APP_NAME__
      ref: __CI_ENVIRONMENT_SLUG__
  template:
    metadata:
      labels:
        app: __CUST_APP_NAME__
        ref: __CI_ENVIRONMENT_SLUG__
    spec:
      imagePullSecrets:
        - name: auth-harbor
      terminationGracePeriodSeconds: 45
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: scaling-type
                    operator: In
                    values:
                      - spot
      containers:
        - name: __CUST_APP_NAME__
          image: __CUST_APP_IMAGE_NAME__
          imagePullPolicy: IfNotPresent
          env:
            - name: ENV
              value: __CUST_ENV__
            - name: SERVICE
              value: __CUST_SERVICE__
          resources:
            requests:
              memory: 128Mi
              cpu: 0.1
            limits:
              memory: 1024Mi
              # cpu: 0.5
          readinessProbe:
            httpGet:
              # 探针
              path: /check-service
              port: __CUST_PORT_APP__
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 1
          livenessProbe:
            tcpSocket:
              port: __CUST_PORT_APP__
            initialDelaySeconds: 120
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 1
          startupProbe:
            httpGet:
              path: /check-service
              port: __CUST_PORT_APP__
            initialDelaySeconds: 15
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 12
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - 'sleep 15'
