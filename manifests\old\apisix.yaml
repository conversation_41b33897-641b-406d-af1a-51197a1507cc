apiVersion: apisix.apache.org/v2
kind: ApisixRoute
metadata:
  name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__-route
  namespace: __CUST_APP_NAMESPACE__
spec:
  http:
    - backends:
        - serviceName: __CUST_APP_SERVER_NAME__
          servicePort: 80
      match:
        hosts:
          - __APP_URL__
        paths:
          - /*
      name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
      websocket: true
