---
inclusion: manual
---

# 外部搜索优先使用 Bocha 搜索引擎规范

## 关键规则

- 当需要进行互联网搜索时，优先使用`bocha-ai-search`工具
- 对于中文搜索查询，必须使用 Bocha 搜索而非其他搜索工具
- 搜索结果应以 Markdown 格式呈现，确保良好的可读性
- 对于需要详细内容的搜索，设置`summary`参数为`true`以获取更完整的摘要
- 在结果中保留原始 URL 链接，以便用户可以访问原始来源
- 技术类搜索内容应当增加结果数量，设置`count`参数为较大值（15-20）
- 时效性内容搜索时，应设置适当的`freshness`参数限制结果时间范围
- 每次搜索后应对结果进行简要总结，提取关键信息
- 对搜索结果进行适当分类和组织，提高信息获取效率
- 当搜索结果不满足需求时，尝试调整搜索关键词，而不是更换搜索工具

## 正确使用示例

```markdown
用户问题：请查询最近的 Vue3 相关新闻

// 使用 Bocha 搜索获取最新 Vue3 相关内容我将使用 Bocha 搜索引擎查询最近的 Vue3 相关新闻。

// 工具调用示例 bocha-ai-search( query: "Vue3 最新特性 新闻", freshness: "OneMonth", count: 15, summary: true )

// 搜索结果示例

## Vue3 最新动态汇总

以下是最近关于 Vue3 的重要新闻和更新：

1. **Vue 3.3 版本发布** - Vue 团队发布了 Vue 3.3 版本，带来了多项性能改进和新特性，包括更好的 TypeScript 集成和优化的编译器。
   [阅读原文](https://blog.vuejs.org/posts/vue-3-3)

2. **Vue 3 生态系统更新** - Pinia 状态管理库发布 2.0 版本，成为 Vue 官方推荐的状态管理解决方案，取代 Vuex。
   [阅读详情](https://pinia.vuejs.org/blog/announcing-pinia-2)

这些更新显示 Vue3 生态系统正在快速发展，特别是在 TypeScript 支持和性能优化方面取得了显著进展。
```
