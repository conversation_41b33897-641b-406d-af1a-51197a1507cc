---
description:
globs:
alwaysApply: false
---
---
description: 此规则适用于在Vue 2.7项目中使用Composition API的场景。应在以下情况应用：(1)创建新的使用Composition API的Vue组件，(2)修改现有的使用Composition API的组件，(3)将选项式API或类组件重构为Composition API，(4)评估组件架构时。此规则确保代码的一致性、可维护性和类型安全，对于大型应用和团队协作尤为重要。
globs: 
alwaysApply: false
---

# Vue 2.7 Composition API 开发规范

## 关键规则

- 使用`<script lang="ts">` + `setup()` 函数实现组合式API，而非Vue 3的`<script setup>`
- 所有从`setup()`返回的数据和方法必须显式声明类型
- 使用`defineComponent`包装组件以获得更好的类型推导
- Props类型定义使用PropType，如`type: Object as PropType<User>`
- 使用`ref<T>()`, `reactive<T>()` 等明确指定响应式数据的类型
- 生命周期钩子映射：使用 `onMounted`, `onUnmounted` 等替代选项式API中的生命周期方法
- 使用TypeScript接口定义复杂数据结构和组件props
- 组件事件使用`context.emit('eventName', payload)`方式触发
- 采用`useXxx`命名约定创建可重用的组合式函数
- 使用`computed<T>(() => {})` 为计算属性提供类型
- 暴露给父组件的方法和属性通过显式的return语句返回
- 复杂业务逻辑抽取到`/composables`或组件相关目录下的组合式函数中

## 示例

<example>
正确的Vue 2.7 Composition API用法:

```vue
<template>
  <div>
    <p>Count: {{ count }}</p>
    <button @click="increment">Increment</button>
    <div>
      <h3>User Info</h3>
      <input ref="nameInput" v-model="user.name" placeholder="用户名" />
      <p>Email: {{ user.email }}</p>
      <button @click="selectUser">选择用户</button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, onMounted, watch, PropType } from 'vue'

// 类型定义
interface User {
  id: number
  name: string
  email: string
}

export default defineComponent({
  name: 'UserCounter',
  
  props: {
    initialCount: {
      type: Number,
      default: 0
    },
    userData: {
      type: Object as PropType<User>,
      default: () => ({ id: 0, name: '', email: '' })
    }
  },
  
  emits: ['increment', 'userSelected'],
  
  setup(props, { emit, refs }) {
    // 响应式状态
    const count = ref<number>(props.initialCount)
    const user = reactive<User>({ ...props.userData })
    const nameInput = ref<HTMLInputElement | null>(null)
    
    // 计算属性
    const doubleCount = computed<number>(() => count.value * 2)
    
    // 侦听器
    watch(count, (newValue, oldValue) => {
      console.log(`Count changed from ${oldValue} to ${newValue}`)
    })
    
    // 生命周期钩子
    onMounted(() => {
      if (nameInput.value) {
        nameInput.value.focus()
      }
    })
    
    // 方法
    const increment = (): void => {
      count.value++
      emit('increment', count.value)
    }
    
    const selectUser = (): void => {
      emit('userSelected', user)
    }
    
    // 暴露给模板
    return {
      count,
      doubleCount,
      user,
      nameInput,
      increment,
      selectUser
    }
  }
})
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

可复用的组合式函数:

```typescript
// src/composables/useDeviceData.ts
import { ref, computed, onMounted, Ref, ComputedRef } from 'vue'
import { fetchDevices, DeviceInfo } from '@/api/devices'

interface UseDeviceDataReturn {
  devices: Ref<DeviceInfo[]>
  activeDevices: ComputedRef<DeviceInfo[]>
  loading: Ref<boolean>
  error: Ref<Error | null>
  loadDevices: () => Promise<void>
}

export function useDeviceData(limit: number = 10): UseDeviceDataReturn {
  const devices = ref<DeviceInfo[]>([])
  const loading = ref<boolean>(true)
  const error = ref<Error | null>(null)
  
  const activeDevices = computed<DeviceInfo[]>(() => 
    devices.value.filter(device => device.status === 'active')
  )
  
  const loadDevices = async (): Promise<void> => {
    loading.value = true
    try {
      devices.value = await fetchDevices(limit)
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }
  
  onMounted(() => {
    loadDevices()
  })
  
  return {
    devices,
    activeDevices,
    loading,
    error,
    loadDevices
  }
}
```
</example>

<example type="invalid">
错误的Vue 2.7 Composition API用法:

```vue
<template>
  <div>
    <p>Count: {{ count }}</p>
    <button @click="increment">Increment</button>
  </div>
</template>

<script setup lang="ts">
// 错误: Vue 2.7不支持<script setup>语法
import { ref } from 'vue'

const count = ref(0)  // 错误: 缺少类型注解

function increment() {
  count++  // 错误: 没有使用.value访问ref值
}
</script>
```

或者:

```vue
<template>
  <div>
    <p>Count: {{ count }}</p>
    <button @click="increment">Increment</button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ref, reactive } from 'vue'

export default {
  // 错误: 未使用defineComponent获得更好的类型支持
  setup(props) {
    const count = props.initialCount  // 错误: 未使用ref/reactive保持响应性
    
    function increment() {
      count++  // 错误: 直接修改值，非响应式
      this.$emit('increment', count)  // 错误: 在setup中使用this
    }
    
    return {
      count,
      increment
    }
  },
  // 错误: 混合使用选项式API和组合式API
  mounted() {
    this.$refs.nameInput.focus()
  }
}
</script>
```
</example>
