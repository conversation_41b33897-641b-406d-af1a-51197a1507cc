---
inclusion: fileMatch
fileMatchPattern: '**/*.vue'
---

# 组件结构规范

## 关键规则

- 组件优先使用文件夹结构组织，推荐的组织方式为：`index.vue`、`index.ts`(可选)和`types.ts`(可选)
- 使用 TypeScript 时，`types.ts`必须包含所有组件相关的类型定义和接口，使用`export type`和`export interface`导出
- 当组件需要全局注册时，`index.ts`需要封装组件安装逻辑，并使用`export default`导出组件
- 支持以下两种组件编写方式：
  - Vue 3 风格：`<script setup lang="ts">` (优先，用于 submodule/ent-module)
  - Vue 2.7 风格：常规`<script>`标签，搭配选项式 API 或`setup`函数
- 组件样式应使用`<style lang="scss" scoped>`并避免全局污染
- 组件必须具有明确的类型定义，包括 props、emits 和其他相关类型

## 组件目录结构示例

```
src/components/Example/
  ├── index.vue     # 组件主文件
  ├── index.ts      # 导出文件（可选）
  └── types.ts      # 类型定义（可选）
```

## Vue 3 风格组件示例（适用于 ent-module 子应用）

```vue
<template>
  <div class="example">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import type { ExampleProps } from './types'

  const props = defineProps<ExampleProps>()
  const emit = defineEmits<{
    (e: 'change', value: string): void
    (e: 'update', value: string): void
  }>()

  // 组件逻辑
  const value = ref(props.defaultValue || '')

  // 方法定义
  function handleChange(newValue: string) {
    value.value = newValue
    emit('change', newValue)
  }
</script>

<style lang="scss" scoped>
  .example {
    // 组件样式
  }
</style>
```

## Vue 2.7 风格组件示例

```vue
<template>
  <div class="example">
    <!-- 组件内容 -->
  </div>
</template>

<script>
  export default {
    name: 'Example',
    props: {
      options: {
        type: Array,
        default: () => []
      },
      defaultValue: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        value: this.defaultValue
      }
    },
    methods: {
      handleChange(newValue) {
        this.value = newValue
        this.$emit('change', newValue)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .example {
    // 组件样式
  }
</style>
```
