---
description: 
globs: 
alwaysApply: true
---
# 业务逻辑复用原则

## 关键规则

- 永远以业务逻辑复用性为第一原则，对相似功能进行抽象和泛化
- 将多个页面共用的业务逻辑必须抽离到适当的composables文件中
- 页面组件应主要由多个composables组合而成，保持页面代码简洁清晰
- 业务逻辑应按功能职责拆分成多个较小的composables，而非单个大型composable
- 所有composables必须定义明确的输入参数和返回值类型
- 抽象出的composables应具有足够的灵活性，通过参数控制细节差异
- 修改共用逻辑时，必须评估其对所有使用处的影响，确保一处修改多处正确生效
- 公共业务逻辑应有充分的单元测试覆盖，确保稳定性
- 支持Vue 2.7的组合式API和Vue 3的script setup语法，保持一致的代码风格

## 示例

<example>
✅ 正确的业务逻辑复用:

// 页面级别的组合：使用多个复用的composables（使用script setup语法）
```vue
<!-- src/routes/device/manage/pages/tiantong/index.vue -->
<script setup lang="ts">
import { useDeviceList } from "../../../composables/useDeviceList";
import { useDeviceFilter } from "../../../composables/useDeviceFilter";
import { useDeviceActions } from "../../../composables/useDeviceActions";
import { usePermission } from "@/hooks/usePermission";

// 获取设备列表逻辑
const {
  devices,
  loading,
  pagination,
  fetchDevices
} = useDeviceList('tiantong');

// 过滤逻辑
const {
  filterForm,
  filterVisible,
  applyFilter,
  resetFilter
} = useDeviceFilter(fetchDevices);

// 设备操作逻辑
const {
  handleEdit,
  handleDelete,
  handleExport
} = useDeviceActions('tiantong');

// 权限控制
const { hasPermission } = usePermission('device:tiantong');
</script>
```

// 使用Vue 2.7选项式API的组合式API写法
```vue
<!-- src/routes/device/manage/pages/beidou/index.vue -->
<script>
import { useDeviceList } from "../../../composables/useDeviceList";
import { useDeviceFilter } from "../../../composables/useDeviceFilter";
import { useDeviceActions } from "../../../composables/useDeviceActions";
import { usePermission } from "@/hooks/usePermission";

export default {
  name: 'DeviceManageBeidou',
  setup() {
    // 获取设备列表逻辑
    const {
      devices,
      loading,
      pagination,
      fetchDevices
    } = useDeviceList('beidou');

    // 过滤逻辑
    const {
      filterForm,
      filterVisible,
      applyFilter,
      resetFilter
    } = useDeviceFilter(fetchDevices);

    // 设备操作逻辑
    const {
      handleEdit,
      handleDelete,
      handleExport
    } = useDeviceActions('beidou');

    // 权限控制
    const { hasPermission } = usePermission('device:beidou');

    return {
      devices,
      loading,
      pagination,
      fetchDevices,
      filterForm,
      filterVisible,
      applyFilter,
      resetFilter,
      handleEdit,
      handleDelete,
      handleExport,
      hasPermission
    }
  }
}
</script>
```

// 抽象的可复用业务逻辑
```typescript
// src/routes/device/manage/composables/useDeviceList.ts
import { ref, reactive, onMounted } from 'vue';
import type { Device, PaginationConfig } from '../types';
import { getDeviceList } from '@/api/methods/device';

export function useDeviceList(platformType: string) {
  const devices = ref<Device[]>([]);
  const loading = ref(false);
  const pagination = reactive<PaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const fetchDevices = async (params = {}) => {
    loading.value = true;
    try {
      const { data, total } = await getDeviceList({
        platformType,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params
      });
      devices.value = data;
      pagination.total = total;
    } catch (error) {
      console.error('获取设备列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchDevices();
  });

  return {
    devices,
    loading,
    pagination,
    fetchDevices
  };
}
```
</example>

<example type="invalid">
❌ 错误的业务逻辑复用:

// 错误：在每个页面中重复相似的业务逻辑
```vue
<!-- src/routes/device/manage/pages/tiantong/index.vue -->
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { getDeviceList } from '@/api/methods/device';

// 错误：直接在页面中实现业务逻辑，没有抽象复用
const devices = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

const fetchDevices = async () => {
  loading.value = true;
  try {
    const { data, total } = await getDeviceList({
      platformType: 'tiantong',
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    });
    devices.value = data;
    pagination.total = total;
  } catch (error) {
    console.error('获取设备列表失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchDevices();
});

// 错误：其他页面也会有几乎相同的代码，造成大量重复
</script>
```

<!-- src/routes/device/manage/pages/beidou/index.vue -->
```vue
<script>
import { ref, reactive, onMounted } from 'vue';
import { getDeviceList } from '@/api/methods/device';

export default {
  name: 'DeviceManageBeidou',
  data() {
    // 错误：使用data函数而非组合式API，且与其他页面重复逻辑
    return {
      devices: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  methods: {
    async fetchDevices() {
      this.loading = true;
      try {
        const { data, total } = await getDeviceList({
          platformType: 'beidou',  // 唯一的差异
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize
        });
        this.devices = data;
        this.pagination.total = total;
      } catch (error) {
        console.error('获取设备列表失败:', error);
      } finally {
        this.loading = false;
      }
    }
  },
  mounted() {
    this.fetchDevices();
  }
}
</script>
```
</example>
