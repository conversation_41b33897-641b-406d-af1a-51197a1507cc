# 1. API 版本和资源类型
apiVersion: autoscaling/v2 # 使用 autoscaling API 的 v2 版本
kind: HorizontalPodAutoscaler # 声明水平自动扩缩容资源

# 2. 元数据配置
metadata:
  # HPA 资源名称，包含三个变量：
  # - __CUST_APP_NAME__: 自定义应用名称（如 my-app）
  # - __CI_ENVIRONMENT_SLUG__: 环境标识（如 prod）
  # - cpu-keda-scaler: 表示基于 CPU 的自动扩缩容
  name: __CUST_APP_NAME__-__CI_ENVIRONMENT_SLUG__-cpu-keda-scaler

  # 目标命名空间
  # __CUST_APP_NAMESPACE__: 应用所在的命名空间（如 webb)
  namespace: __CUST_APP_NAMESPACE__

# 3. 核心配置
spec:
  # 目标工作负载配置
  scaleTargetRef:
    apiVersion: apps/v1 # 目标资源的 API 版本
    kind: Deployment # 目标资源类型（支持 Deployment/StatefulSet）
    # 目标 Deployment 名称
    # __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__（如 ent-paas-prod）
    name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__

  # 副本数范围
  minReplicas: __CUST_MIN_RC__ # 最小副本数（如 2）
  maxReplicas: __CUST_MAX_RC__ # 最大副本数（如 10）

  # 扩缩容指标配置
  metrics:
    - type: Resource # 资源类型指标
      resource:
        name: cpu # 监控 CPU 资源
        target:
          type: Utilization # 使用率类型
          # CPU 平均使用率阈值（百分比）
          averageUtilization: __AUTO_SCALER_AVERAGE_UTILIZATION__
