---
description: 
globs: 
alwaysApply: false
---
---
description: 此规则适用于在Vue 2.7项目中使用Pinia状态管理的场景。应在以下情况应用：(1)创建新的Store，(2)修改现有Store，(3)在组件中使用Store，(4)配置Pinia插件。此规则确保状态管理的一致性、可维护性和类型安全，对于大型应用的状态管理尤为重要。
globs: 
alwaysApply: false
---

# Pinia 与 Vue 2.7 集成规范

## 关键规则

- 使用`createPinia`和`PiniaVuePlugin`在Vue 2.7项目中初始化Pinia
- 所有Store定义使用组合式函数风格（`defineStore`的setup语法）
- Store文件应放在`src/stores`或`src/submodule/ent-module/stores`目录中
- Store ID必须唯一且具有描述性，使用kebab-case命名（如`app-state`）
- 每个Store使用TypeScript接口明确定义其状态类型
- 在Store中的响应式状态使用`ref()`, `reactive()`, `computed()`等Composition API
- Store中的复杂逻辑应提取到辅助函数中，保持Store本身简洁
- 在组件中通过`useXxxStore()`风格的函数访问Store
- 对于需要持久化的Store，使用pinia-plugin-persistedstate插件
- 使用`$reset()`功能时，确保Store实现了reset方法或使用fix-pinia-reset-plugin
- Store之间的交互应通过显式导入和调用，避免隐式依赖
- 所有Store导出和引入使用命名导出/导入，而非默认导出

## 示例

<example>
正确的Pinia在Vue 2.7中的使用:

// src/plugins/pinia.ts - Pinia初始化
```typescript
import Vue from 'vue'
import { PiniaVuePlugin, createPinia } from 'pinia'
import fixPiniaResetPlugin from './fix-pinia-reset-plugin'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

Vue.use(PiniaVuePlugin) // pinia的vue2安装方式

const pinia = createPinia()
pinia.use(fixPiniaResetPlugin)
pinia.use(piniaPluginPersistedstate)

export default pinia
```

// src/store/user.ts - Store定义
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types'
import { getUserProfile } from '@/services/user'

// 定义Store
export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)
  
  // Getters (计算属性)
  const isLoggedIn = computed(() => !!user.value)
  const userRole = computed(() => user.value?.role || 'guest')
  
  // Actions (方法)
  async function fetchUserProfile(userId: string) {
    loading.value = true
    error.value = null
    
    try {
      user.value = await getUserProfile(userId)
    } catch (err) {
      error.value = err as Error
      user.value = null
    } finally {
      loading.value = false
    }
  }
  
  function logout() {
    user.value = null
  }
  
  return {
    // 状态
    user,
    loading,
    error,
    
    // Getters
    isLoggedIn,
    userRole,
    
    // Actions
    fetchUserProfile,
    logout
  }
}, {
  // 启用持久化
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['user']
  }
})
```

// 在组件中使用Store
```vue
<template>
  <div>
    <div v-if="loading">加载中...</div>
    <div v-else-if="user">
      <h2>欢迎, {{ user.name }}</h2>
      <p>角色: {{ userRole }}</p>
      <button @click="logout">退出登录</button>
    </div>
    <div v-else>
      <p>请登录</p>
      <button @click="handleLogin">登录</button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

export default defineComponent({
  name: 'UserProfile',
  
  setup() {
    const userStore = useUserStore()
    
    onMounted(() => {
      // 页面加载时检查用户状态
      if (!userStore.isLoggedIn && localStorage.getItem('userId')) {
        userStore.fetchUserProfile(localStorage.getItem('userId')!)
      }
    })
    
    const handleLogin = async () => {
      // 示例登录逻辑
      await userStore.fetchUserProfile('user-123')
      localStorage.setItem('userId', 'user-123')
    }
    
    return {
      // 暴露 store 的状态和方法给模板
      user: userStore.user,
      loading: userStore.loading,
      userRole: userStore.userRole,
      logout: userStore.logout,
      handleLogin
    }
  }
})
</script>
```
</example>

<example type="invalid">
错误的Pinia在Vue 2.7中的使用:

// 错误: 使用选项式风格定义Store而非组合式函数风格
```typescript
import { defineStore } from 'pinia'

export const useCounterStore = defineStore({
  id: 'counter',
  state: () => ({
    count: 0
  }),
  getters: {
    doubleCount(state) {
      return state.count * 2
    }
  },
  actions: {
    increment() {
      this.count++
    }
  }
})
```

// 错误: 不恰当的Store文件位置
```typescript
// src/views/user/store.ts - 错误位置
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usePageStore = defineStore('user-page', () => {
  const filter = ref('')
  return { filter }
})
```

// 错误: 在组件中使用Store的不正确方式
```vue
<script>
// 错误: 未使用TypeScript
export default {
  data() {
    return {
      user: null
    }
  },
  mounted() {
    // 错误: 使用选项式API混合Pinia
    this.user = this.$pinia.state.value.user  // 错误: 不应直接访问pinia.state
  },
  methods: {
    login() {
      // 错误: 不使用store实例的方法
      this.$pinia.state.value.user = { id: 1, name: 'User' }  // 错误: 不应直接修改state
    }
  }
}
</script>
```
</example>
