---
description: 性能优先开发规则
globs: 
alwaysApply: false
---
# 性能优先开发规则

## 关键规则

- 在编写任何新功能代码之前，必须先进行性能影响评估和预期性能目标设定
- 所有涉及循环、递归、大数据处理的代码必须包含时间复杂度和空间复杂度分析
- 前端组件开发必须考虑渲染性能、包大小影响、内存泄漏防护
- 数据库查询和API调用必须包含性能优化策略（索引、缓存、分页等）
- 异步操作必须设置合理的超时和错误处理，避免阻塞用户界面
- 大型数组操作必须使用虚拟化或分批处理，避免一次性渲染过多DOM元素
- 所有新增的第三方依赖必须评估其对包大小和运行时性能的影响
- 关键用户路径（登录、搜索、数据加载）必须设定明确的性能基准和监控
- 代码提交前必须运行性能测试，确保没有明显的性能回退
- 复杂计算或数据处理优先考虑使用Web Workers避免主线程阻塞

## 示例

<example>
  ✅ 正确的性能优先开发流程：

  ```typescript
  // 1. 性能目标设定和评估
  /**
   * 企业列表组件性能目标:
   * - 初始渲染时间: < 200ms
   * - 滚动流畅度: 60fps
   * - 内存使用: < 50MB
   * - 网络请求: < 2秒
   */

  // 2. 使用虚拟化处理大数据量
  <template>
    <div class="enterprise-list">
      <!-- 使用虚拟滚动避免渲染1000+企业项 -->
      <RecycleScroller
        class="scroller"
        :items="enterprises"
        :item-size="60"
        key-field="id"
        v-slot="{ item }"
      >
        <EnterpriseItem :enterprise="item" />
      </RecycleScroller>
    </div>
  </template>

  // 3. 优化网络请求和缓存策略
  export function useEnterpriseList() {
    const cache = new Map(); // 实现缓存避免重复请求
    
    const fetchEnterprises = async (params) => {
      const cacheKey = JSON.stringify(params);
      if (cache.has(cacheKey)) {
        return cache.get(cacheKey);
      }
      
      // 分页加载，避免一次性加载大量数据
      const result = await api.getEnterprises({
        ...params,
        pageSize: 50 // 限制单次加载数量
      });
      
      cache.set(cacheKey, result);
      return result;
    };
    
    return { fetchEnterprises };
  }

  // 4. 防抖优化搜索性能
  const searchQuery = ref('');
  const debouncedSearch = debounce(async (query) => {
    if (query.length < 2) return; // 避免无效搜索
    await searchEnterprises(query);
  }, 300);

  // 5. 内存泄漏防护
  onUnmounted(() => {
    cache.clear(); // 清理缓存防止内存泄漏
    // 取消未完成的请求
    controller.abort();
  });
  ```

  代码审查时的性能检查清单：
  - ✅ 时间复杂度分析：O(n) 虚拟滚动
  - ✅ 空间复杂度控制：缓存大小限制
  - ✅ 网络优化：分页加载 + 缓存
  - ✅ 用户体验：防抖搜索 + 加载状态
  - ✅ 内存管理：组件卸载时清理资源
</example>

<example type="invalid">
  ❌ 错误的忽视性能开发方式：

  ```typescript
  // 错误1：未考虑性能影响直接实现
  <template>
    <div class="enterprise-list">
      <!-- 错误：直接渲染1000+企业项，导致页面卡顿 -->
      <div 
        v-for="enterprise in allEnterprises" 
        :key="enterprise.id"
        class="enterprise-item"
      >
        {{ enterprise.name }}
      </div>
    </div>
  </template>

  // 错误2：未优化的数据获取
  export function useEnterpriseList() {
    const fetchAllEnterprises = async () => {
      // 错误：一次性加载所有数据，无分页
      const result = await api.getAllEnterprises();
      // 错误：无缓存策略，每次都重新请求
      return result.data;
    };
    
    return { fetchAllEnterprises };
  }

  // 错误3：未优化的搜索实现
  watch(searchQuery, async (newQuery) => {
    // 错误：每次输入都立即搜索，无防抖
    if (newQuery) {
      await searchEnterprises(newQuery);
    }
  });

  // 错误4：无内存管理
  const cache = new Map();
  // 错误：组件卸载时未清理缓存，导致内存泄漏
  // 错误：无请求取消机制，可能导致竞态条件

  // 错误5：复杂计算阻塞主线程
  function processLargeDataset(data) {
    // 错误：在主线程进行大量计算
    for (let i = 0; i < 1000000; i++) {
      // 复杂计算逻辑
      data.forEach(item => {
        // 更多计算...
      });
    }
    return processedData;
  }
  ```

  代码问题总结：
  - ❌ 无性能目标设定
  - ❌ 未考虑大数据量渲染性能
  - ❌ 网络请求未优化
  - ❌ 无用户交互优化
  - ❌ 存在内存泄漏风险
  - ❌ 主线程阻塞问题
</example>
