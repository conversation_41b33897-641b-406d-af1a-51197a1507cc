---
inclusion: manual
---

# Kiro Rule Generation Standards

## Critical Rules for Creating Steering Files

- All steering files must be located in `.kiro/steering/` directory
- Use descriptive kebab-case filenames ending in `.md`
- Always include front matter with `inclusion` field specifying rule type
- Rule inclusion types:
  - `always` - Applied to all interactions
  - `fileMatch` with `fileMatchPattern` - Applied when specific file patterns are in context
  - `manual` - Applied when explicitly referenced or needed

## Front Matter Structure

```yaml
---
inclusion: always|fileMatch|manual
fileMatchPattern: '**/*.{ext}' # only for fileMatch type
---
```

## Content Structure

````markdown
# Rule Title

## Critical Rules (or Key Rules)

- Concise, bulleted list of actionable rules that must be followed
- Focus on specific, measurable behaviors
- Use clear, direct language

## Examples (when helpful)

**Good Example:** `code or description`

**Avoid:** `code or description`
````

## Organizational Guidelines

- **Always Active**: Rules that apply to every interaction (emoji communication, package management)
- **File-Matched**: Rules that apply when specific file types are in context (TypeScript, Vue, etc.)
- **Manual**: Rules that are activated when explicitly needed (git conventions, search tools)

## Content Best Practices

- Focus on actionable, clear directives without unnecessary explanation
- Use concise Markdown tailored for AI context window usage
- Include valid and invalid examples when helpful
- Be judicious with content length as it impacts performance
- Focus on essential information that helps the agent make decisions
- Use bullet points for rules, code blocks for examples
- Maintain consistency with existing steering file formats

## File Naming Conventions

- Use descriptive names that clearly indicate the rule's purpose
- Use kebab-case (lowercase with hyphens)
- Examples: `typescript-best-practices.md`, `vue27-composition-api.md`, `git-commit-conventions.md`

## Quality Checklist

- [ ] Front matter includes correct inclusion type
- [ ] Rules are specific and actionable
- [ ] Examples are clear and relevant
- [ ] Content is concise but comprehensive
- [ ] File is properly named and located
- [ ] Rule addresses a specific development need
