---
inclusion: fileMatch
fileMatchPattern: '**/*.{ts,js}'
---

# Pinia 与 Vue 2.7 集成规范

## 关键规则

- 使用`createPinia`和`PiniaVuePlugin`在 Vue 2.7 项目中初始化 Pinia
- 所有 Store 定义使用组合式函数风格（`defineStore`的 setup 语法）
- Store 文件应放在`src/stores`或`src/submodule/ent-module/stores`目录中
- Store ID 必须唯一且具有描述性，使用 kebab-case 命名（如`app-state`）
- 每个 Store 使用 TypeScript 接口明确定义其状态类型
- 在 Store 中的响应式状态使用`ref()`, `reactive()`, `computed()`等 Composition API
- Store 中的复杂逻辑应提取到辅助函数中，保持 Store 本身简洁
- 在组件中通过`useXxxStore()`风格的函数访问 Store
- 对于需要持久化的 Store，使用 pinia-plugin-persistedstate 插件
- 使用`$reset()`功能时，确保 Store 实现了 reset 方法或使用 fix-pinia-reset-plugin
- Store 之间的交互应通过显式导入和调用，避免隐式依赖
- 所有 Store 导出和引入使用命名导出/导入，而非默认导出

## Pinia 初始化示例

```typescript
// src/plugins/pinia.ts - Pinia初始化
import Vue from 'vue'
import { PiniaVuePlugin, createPinia } from 'pinia'
import fixPiniaResetPlugin from './fix-pinia-reset-plugin'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

Vue.use(PiniaVuePlugin) // pinia的vue2安装方式

const pinia = createPinia()
pinia.use(fixPiniaResetPlugin)
pinia.use(piniaPluginPersistedstate)

export default pinia
```

## Store 定义示例

```typescript
// src/store/user.ts - Store定义
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types'
import { getUserProfile } from '@/services/user'

// 定义Store
export const useUserStore = defineStore(
  'user',
  () => {
    // 状态
    const user = ref<User | null>(null)
    const loading = ref(false)
    const error = ref<Error | null>(null)

    // Getters (计算属性)
    const isLoggedIn = computed(() => !!user.value)
    const userRole = computed(() => user.value?.role || 'guest')

    // Actions (方法)
    async function fetchUserProfile(userId: string) {
      loading.value = true
      error.value = null

      try {
        user.value = await getUserProfile(userId)
      } catch (err) {
        error.value = err as Error
        user.value = null
      } finally {
        loading.value = false
      }
    }

    function logout() {
      user.value = null
    }

    return {
      // 状态
      user,
      loading,
      error,

      // Getters
      isLoggedIn,
      userRole,

      // Actions
      fetchUserProfile,
      logout
    }
  },
  {
    // 启用持久化
    persist: {
      key: 'user-store',
      storage: localStorage,
      paths: ['user']
    }
  }
)
```

## 在组件中使用 Store

```vue
<template>
  <div>
    <div v-if="loading">加载中...</div>
    <div v-else-if="user">
      <h2>欢迎, {{ user.name }}</h2>
      <p>角色: {{ userRole }}</p>
      <button @click="logout">退出登录</button>
    </div>
    <div v-else>
      <p>请登录</p>
      <button @click="handleLogin">登录</button>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, onMounted } from 'vue'
  import { useUserStore } from '@/store/user'

  export default defineComponent({
    name: 'UserProfile',

    setup() {
      const userStore = useUserStore()

      onMounted(() => {
        // 页面加载时检查用户状态
        if (!userStore.isLoggedIn && localStorage.getItem('userId')) {
          userStore.fetchUserProfile(localStorage.getItem('userId')!)
        }
      })

      const handleLogin = async () => {
        // 示例登录逻辑
        await userStore.fetchUserProfile('user-123')
        localStorage.setItem('userId', 'user-123')
      }

      return {
        // 暴露 store 的状态和方法给模板
        user: userStore.user,
        loading: userStore.loading,
        userRole: userStore.userRole,
        logout: userStore.logout,
        handleLogin
      }
    }
  })
</script>
```
