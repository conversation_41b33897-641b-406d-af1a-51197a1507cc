---
inclusion: fileMatch
fileMatchPattern: '**/*.{ts,vue}'
---

# TypeScript 与 Vue 2.7 集成规范

## 关键规则

- 所有 Vue 组件文件使用`<script lang="ts">`声明 TypeScript 支持
- 使用`defineComponent`包装组件以获得更好的类型推导
- 使用`PropType<T>`为组件 props 提供复杂类型定义
- 明确定义所有函数参数和返回类型
- 为响应式数据提供明确的类型注解：`ref<string>('')`, `reactive<User>({})`
- 使用接口（interface）而非类型别名（type）定义对象结构，除非需要联合类型
- 当使用类组件时，应用`vue-property-decorator`和`vue-class-component`
- 使用 TypeScript 命名空间（namespace）组织相关的接口和类型
- API 请求和响应应有明确的类型定义，使用`Awaited<ReturnType<T>>`获取 Promise 解析类型
- 使用类型断言（`as`）而非类型转换，如`userData as User`
- 事件处理函数参数使用具体类型，而非`any`或`unknown`
- 避免使用`any`，优先使用`unknown`配合类型守卫
- 组件模板引用使用泛型：`ref<HTMLElement | null>(null)`
- 使用类型交叉（`&`）组合类型，而非继承和扩展（extends）

## 示例

**正确的 TypeScript 与 Vue 2.7 集成:**

```vue
<template>
  <!-- 组件模板 -->
</template>

<script lang="ts">
  import { defineComponent, ref, PropType, computed } from 'vue'

  // 定义接口
  interface UserData {
    id: number
    name: string
    email: string
    role: 'admin' | 'user' | 'guest'
  }

  // 使用defineComponent
  export default defineComponent({
    name: 'UserProfile',

    props: {
      user: {
        type: Object as PropType<UserData>,
        required: true
      },
      isEditable: {
        type: Boolean,
        default: false
      }
    },

    emits: ['update:user', 'delete'],

    setup(props, { emit }) {
      // 带类型的响应式数据
      const isEditing = ref<boolean>(false)
      const editableUser = ref<UserData>({ ...props.user })

      // 带类型的计算属性
      const isAdmin = computed<boolean>(() => props.user.role === 'admin')

      // 带类型的函数
      function startEditing(): void {
        isEditing.value = true
        editableUser.value = { ...props.user }
      }

      // 带类型的事件处理函数
      function handleSubmit(event: Event): void {
        event.preventDefault()
        emit('update:user', editableUser.value)
        isEditing.value = false
      }

      return {
        isEditing,
        editableUser,
        isAdmin,
        startEditing,
        handleSubmit
      }
    }
  })
</script>
```

**类型定义文件示例:**

```typescript
// 使用命名空间组织相关类型
export namespace User {
  export interface Profile {
    id: number
    name: string
    email: string
    avatar?: string
    role: Role
  }

  export type Role = 'admin' | 'user' | 'guest'

  export interface Settings {
    theme: 'light' | 'dark' | 'system'
    notifications: boolean
    language: string
  }
}

// API响应类型
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 使用类型交叉组合类型
export type UserWithSettings = User.Profile & User.Settings
```
