---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: __CI_PROJECT_NAME__-__CI_ENVIRONMENT_SLUG__
  # 修改为规划 Namespaces，考虑到项目组太多，若作为命名空间不易管理
  namespace: __CUST_APP_NAMESPACE__
  labels:
    app: __CUST_APP_NAME__
    ref: __CI_ENVIRONMENT_SLUG__
spec:
  # 根据实际需求，配置合理数量副本
  replicas: __CUST_REPLICAS__
  minReadySeconds: 10
  #滚动更新策略      <----------------------
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  selector:
    matchLabels:
      app: __CUST_APP_NAME__
      ref: __CI_ENVIRONMENT_SLUG__
  template:
    metadata:
      labels:
        app: __CUST_APP_NAME__
        ref: __CI_ENVIRONMENT_SLUG__
    spec:
      imagePullSecrets:
        - name: auth-harbor
      #优雅终止时间 <----------------------------
      terminationGracePeriodSeconds: 30
      containers:
        - name: __CUST_APP_NAME__
          image: __CUST_APP_K8S_IMAGE_NAME__
          imagePullPolicy: Always
          env:
            - name: ENV
              value: __CUST_ENV__
            - name: SERVICE
              value: __CUST_SERVICE__
          ports:
            - name: http-metrics
              protocol: TCP
              # 配置对应项目服务端口，无需再进行外部服务端口定义
              containerPort: __CUST_PORT_APP__
          resources:
            requests:
              memory: '200Mi'
              cpu: '300m'
            limits:
              memory: '1000Mi'
              cpu: '600m'
          #就绪探针    <------------------
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            httpGet:
              path: /login #根据业务实际情况调整，大部分为 /index.html
              port: 9000
            timeoutSeconds: 1
          #生命周期控制：优雅关闭  <---------------------------
          lifecycle:
            preStop:
              exec:
                command: ['/bin/sh', '-c', 'sleep 10']
