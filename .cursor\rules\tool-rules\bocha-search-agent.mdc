---
description: 此规则适用于需要进行网络搜索的场景。应在以下情况应用：(1)用户需要获取最新信息，(2)需要查询特定网站内容，(3)搜索中文内容和资源，(4)需要详细的搜索结果摘要。此规则确保搜索结果的质量和相关性，优先使用Bocha搜索以获取更适合中文内容的搜索结果。对于技术文档、新闻资讯、行业动态和学术研究等信息获取尤为重要。
globs: 
alwaysApply: false
---
# 外部搜索优先使用Bocha搜索引擎规范

## 关键规则

- 当需要进行互联网搜索时，优先使用`bocha-ai-search`工具
- 对于中文搜索查询，必须使用Bocha搜索而非其他搜索工具
- 搜索结果应以Markdown格式呈现，确保良好的可读性
- 对于需要详细内容的搜索，设置`summary`参数为`true`以获取更完整的摘要
- 在结果中保留原始URL链接，以便用户可以访问原始来源
- 技术类搜索内容应当增加结果数量，设置`count`参数为较大值（15-20）
- 时效性内容搜索时，应设置适当的`freshness`参数限制结果时间范围
- 每次搜索后应对结果进行简要总结，提取关键信息
- 对搜索结果进行适当分类和组织，提高信息获取效率
- 当搜索结果不满足需求时，尝试调整搜索关键词，而不是更换搜索工具

## 示例

<example>
```markdown
用户问题：请查询最近的Vue3相关新闻

// 使用Bocha搜索获取最新Vue3相关内容
我将使用Bocha搜索引擎查询最近的Vue3相关新闻。

// 工具调用示例
bocha-ai-search(
  query: "Vue3 最新特性 新闻",
  freshness: "OneMonth",
  count: 15,
  summary: true
)

// 搜索结果示例
## Vue3最新动态汇总

以下是最近关于Vue3的重要新闻和更新：

1. **Vue 3.3版本发布** - Vue团队发布了Vue 3.3版本，带来了多项性能改进和新特性，包括更好的TypeScript集成和优化的编译器。
   [阅读原文](mdc:https:/blog.vuejs.org/posts/vue-3-3)

2. **Vue 3生态系统更新** - Pinia状态管理库发布2.0版本，成为Vue官方推荐的状态管理解决方案，取代Vuex。
   [阅读详情](mdc:https:/pinia.vuejs.org/blog/announcing-pinia-2)

3. **Vue 3与React性能对比报告** - 最新的框架性能对比显示Vue 3在渲染大型列表和复杂组件时表现优于React 18。
   [查看报告](mdc:https:/vueschool.io/articles/vue-performance-comparison)

这些更新显示Vue3生态系统正在快速发展，特别是在TypeScript支持和性能优化方面取得了显著进展。
```
</example>

<example type="invalid">
```markdown
用户问题：搜索最近的AI大模型发展

// 错误：直接使用web_search工具而非Bocha搜索
web_search(search_term: "最近的AI大模型发展")

// 错误：未为中文内容使用Bocha搜索
// 错误：未设置合理的freshness参数控制时效性
// 错误：结果未组织成易读格式

这是关于AI大模型的一些搜索结果:
1. OpenAI发布了GPT-4模型...
2. Google推出Gemini模型...
3. Anthropic发布Claude 3...

// 错误：未对搜索结果进行分类和总结
// 错误：未保留原始链接
// 错误：未在结果中提供足够的详细信息
```
</example>
